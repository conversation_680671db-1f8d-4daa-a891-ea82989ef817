const User = require('../models/User');
const UserAuth = require('../models/UserAuth');
const OTP = require('../models/OTP');
const { generateToken } = require('../utils/jwtUtils');
const { generateOTP, calculateOTPExpiry } = require('../utils/otpUtils');
const { sendOTPEmail } = require('../utils/emailUtils');
const mongoose = require('mongoose');
const passport = require('passport');

/**
 * Check if email exists and is verified
 * @route   POST /api/auth/check-email
 * @access  Public
 */
exports.checkEmail = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an email'
      });
    }

    // Check if user exists
    const user = await User.findOne({ email });

    if (!user) {
      // Email doesn't exist at all
      return res.status(200).json({
        success: true,
        exists: false
      });
    }

    // Check if the user is verified
    const userAuth = await UserAuth.findOne({ userId: user._id });

    if (!userAuth) {
      // No auth record found (shouldn't happen normally)
      return res.status(200).json({
        success: true,
        exists: false
      });
    }

    // Only consider the email as existing if it's verified
    return res.status(200).json({
      success: true,
      exists: userAuth.isVerified
    });
  } catch (error) {
    console.error('Check email error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Register a new user and send OTP
 * @route   POST /api/auth/register
 * @access  Public
 */
exports.register = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if all fields are provided
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });

    if (existingUser) {
      // Check if the user is verified
      const existingAuth = await UserAuth.findOne({ userId: existingUser._id });

      if (existingAuth && existingAuth.isVerified) {
        // If user is verified, don't allow re-registration
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      } else {
        // If user exists but is not verified, delete the existing records
        console.log(`Removing unverified user: ${existingUser._id}`);

        // Delete existing OTP records
        await OTP.deleteMany({ userId: existingUser._id });

        // Delete existing auth record
        if (existingAuth) {
          await UserAuth.deleteOne({ _id: existingAuth._id });
        }

        // Delete existing user
        await User.deleteOne({ _id: existingUser._id });
      }
    }

    // Create user profile
    const user = await User.create({
      name,
      email
    });

    // Generate OTP
    const otp = generateOTP();
    const otpExpiry = calculateOTPExpiry();

    // Create auth record
    await UserAuth.create({
      userId: user._id,
      email,
      password,
      authType: 'local', // Explicitly set auth type for manual registration
      isVerified: false
    });

    // Create OTP record
    await OTP.create({
      userId: user._id,
      email,
      code: otp,
      purpose: 'verification',
      expiresAt: otpExpiry
    });

    // Send OTP email
    const emailResult = await sendOTPEmail(email, name, otp);
    console.log('Email sending result:', emailResult);

    res.status(201).json({
      success: true,
      message: 'Registration initiated. Please verify your email with the OTP sent.',
      userId: user._id
    });
  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Verify OTP and complete registration
 * @route   POST /api/auth/verify-otp
 * @access  Public
 */
exports.verifyOTP = async (req, res) => {
  try {
    const { userId, otp } = req.body;

    if (!userId || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Please provide user ID and OTP'
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find OTP record
    const otpRecord = await OTP.findOne({
      userId,
      code: otp,
      purpose: 'verification',
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    // Mark OTP as used
    otpRecord.isUsed = true;
    await otpRecord.save();

    // Mark user as verified
    const userAuth = await UserAuth.findOne({ userId });
    if (!userAuth) {
      return res.status(404).json({
        success: false,
        message: 'Authentication record not found'
      });
    }

    userAuth.isVerified = true;
    await userAuth.save();

    // Generate token with default session (not remember me)
    const token = generateToken(userId, false);

    res.status(200).json({
      success: true,
      message: 'Email verified successfully',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isVerified: true
      }
    });
  } catch (error) {
    console.error('Verify OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Resend OTP
 * @route   POST /api/auth/resend-otp
 * @access  Public
 */
exports.resendOTP = async (req, res) => {
  try {
    const { userId, purpose = 'verification' } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'Please provide user ID'
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if user is already verified (only for verification purpose)
    if (purpose === 'verification') {
      const userAuth = await UserAuth.findOne({ userId });
      if (!userAuth) {
        return res.status(404).json({
          success: false,
          message: 'User authentication record not found'
        });
      }

      if (userAuth.isVerified) {
        return res.status(400).json({
          success: false,
          message: 'User is already verified'
        });
      }
    }

    // Generate new OTP
    const otp = generateOTP();
    const otpExpiry = calculateOTPExpiry();

    // Mark previous OTPs as used
    await OTP.updateMany(
      { userId, purpose, isUsed: false },
      { isUsed: true }
    );

    // Create new OTP record
    await OTP.create({
      userId,
      email: user.email,
      code: otp,
      purpose,
      expiresAt: otpExpiry
    });

    // Send OTP email
    const emailResult = await sendOTPEmail(user.email, user.name, otp);
    console.log('Email sending result:', emailResult);

    res.status(200).json({
      success: true,
      message: 'OTP resent successfully'
    });
  } catch (error) {
    console.error('Resend OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
exports.login = async (req, res) => {
  try {
    const { email, password, rememberMe = false } = req.body;

    // Check if email and password are provided
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Find auth record and include password
    const userAuth = await UserAuth.findOne({ userId: user._id }).select('+password');
    if (!userAuth) {
      return res.status(401).json({
        success: false,
        message: 'Authentication record not found'
      });
    }

    // Check if user is verified
    if (!userAuth.isVerified) {
      // Generate new OTP for unverified user
      const otp = generateOTP();
      const otpExpiry = calculateOTPExpiry();

      // Mark previous OTPs as used
      await OTP.updateMany(
        { userId: user._id, purpose: 'verification', isUsed: false },
        { isUsed: true }
      );

      // Create new OTP record
      await OTP.create({
        userId: user._id,
        email: user.email,
        code: otp,
        purpose: 'verification',
        expiresAt: otpExpiry
      });

      // Send OTP email
      await sendOTPEmail(user.email, user.name, otp);

      return res.status(403).json({
        success: false,
        message: 'Email not verified. A new OTP has been sent to your email.',
        userId: user._id,
        requiresVerification: true
      });
    }

    // Check if password matches
    const isMatch = await userAuth.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login time
    userAuth.lastLogin = new Date();
    await userAuth.save();

    // Generate JWT token with remember me option
    const token = generateToken(user._id, rememberMe);

    res.status(200).json({
      success: true,
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isVerified: userAuth.isVerified
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Handle Google OAuth login/register
 * @route   GET /api/auth/google
 * @access  Public
 */
exports.googleAuth = passport.authenticate('google', { scope: ['profile', 'email'] });

/**
 * Handle Google OAuth callback
 * @route   GET /api/auth/google/callback
 * @access  Public
 */
exports.googleCallback = (req, res, next) => {
  passport.authenticate('google', { session: false }, async (err, user, info) => {
    try {
      // Handle specific error cases
      if (err) {
        console.error('Google auth error:', err);

        // Check for specific error messages
        if (err.message && err.message.includes('already linked to a different Google account')) {
          // This email is already linked to a different Google account
          return res.redirect(`${process.env.FRONTEND_URL}/auth?error=email_already_linked&message=${encodeURIComponent('This email is already linked to a different Google account')}`);
        }

        // Handle other errors
        return res.redirect(`${process.env.FRONTEND_URL}/auth?error=google_auth_failed&message=${encodeURIComponent(err.message || 'Authentication failed')}`);
      }

      if (!user) {
        return res.redirect(`${process.env.FRONTEND_URL}/auth?error=google_auth_failed&message=${encodeURIComponent('Authentication failed')}`);
      }

      // Find user auth record
      const userAuth = await UserAuth.findOne({ userId: user._id });
      if (!userAuth) {
        return res.redirect(`${process.env.FRONTEND_URL}/auth?error=auth_record_not_found&message=${encodeURIComponent('Authentication record not found')}`);
      }

      // Check if this was an account linking
      const isAccountLinking = userAuth.authType === 'google' && userAuth.googleId;

      // Generate token with remember me option (default to true for social login)
      const token = generateToken(user._id, true);

      // Add a flag to indicate if this was an account linking
      const accountLinkingParam = isAccountLinking ? '&accountLinked=true' : '';

      // Redirect to frontend with token and additional info
      res.redirect(`${process.env.FRONTEND_URL}/auth/social-callback?token=${token}&userId=${user._id}${accountLinkingParam}`);
    } catch (error) {
      console.error('Google callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/auth?error=server_error&message=${encodeURIComponent('Server error occurred')}`);
    }
  })(req, res, next);
};

/**
 * Handle GitHub OAuth login/register
 * @route   GET /api/auth/github
 * @access  Public
 */
exports.githubAuth = passport.authenticate('github', { scope: ['user:email'] });

/**
 * Handle GitHub OAuth callback
 * @route   GET /api/auth/github/callback
 * @access  Public
 */
exports.githubCallback = (req, res, next) => {
  passport.authenticate('github', { session: false }, async (err, user, info) => {
    try {
      // Handle specific error cases
      if (err) {
        console.error('GitHub auth error:', err);

        // Check for specific error messages
        if (err.message && err.message.includes('already linked to a different GitHub account')) {
          // This email is already linked to a different GitHub account
          return res.redirect(`${process.env.FRONTEND_URL}/auth?error=email_already_linked&message=${encodeURIComponent('This email is already linked to a different GitHub account')}`);
        }

        if (err.message && err.message.includes('No email found in GitHub profile')) {
          // No email found in GitHub profile
          return res.redirect(`${process.env.FRONTEND_URL}/auth?error=github_no_email&message=${encodeURIComponent('No email found in your GitHub profile. Please make sure your email is public in GitHub settings.')}`);
        }

        // Handle other errors
        return res.redirect(`${process.env.FRONTEND_URL}/auth?error=github_auth_failed&message=${encodeURIComponent(err.message || 'Authentication failed')}`);
      }

      if (!user) {
        return res.redirect(`${process.env.FRONTEND_URL}/auth?error=github_auth_failed&message=${encodeURIComponent('Authentication failed')}`);
      }

      // Find user auth record
      const userAuth = await UserAuth.findOne({ userId: user._id });
      if (!userAuth) {
        return res.redirect(`${process.env.FRONTEND_URL}/auth?error=auth_record_not_found&message=${encodeURIComponent('Authentication record not found')}`);
      }

      // Check if this was an account linking
      const isAccountLinking = userAuth.authType === 'github' && userAuth.githubId;

      // Generate token with remember me option (default to true for social login)
      const token = generateToken(user._id, true);

      // Add a flag to indicate if this was an account linking
      const accountLinkingParam = isAccountLinking ? '&accountLinked=true' : '';

      // Redirect to frontend with token and additional info
      res.redirect(`${process.env.FRONTEND_URL}/auth/social-callback?token=${token}&userId=${user._id}${accountLinkingParam}`);
    } catch (error) {
      console.error('GitHub callback error:', error);
      res.redirect(`${process.env.FRONTEND_URL}/auth?error=server_error&message=${encodeURIComponent('Server error occurred')}`);
    }
  })(req, res, next);
};

/**
 * Get current user profile
 * @route   GET /api/auth/me
 * @access  Private
 */
exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const userAuth = await UserAuth.findOne({ userId: user._id });
    if (!userAuth) {
      return res.status(404).json({
        success: false,
        message: 'Authentication record not found'
      });
    }

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        profilePicture: user.profilePicture,
        coverPhoto: user.coverPhoto,
        bio: user.bio,
        title: user.title,
        location: user.location,
        dateOfBirth: user.dateOfBirth,
        website: user.website,
        socialLinks: user.socialLinks,
        skills: user.skills,
        education: user.education,
        experience: user.experience,
        projects: user.projects,
        isVerified: userAuth.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error('Get me error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Forgot password - send OTP
 * @route   POST /api/auth/forgot-password
 * @access  Public
 */
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Please provide your email'
      });
    }

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'No user with that email'
      });
    }

    // Generate OTP
    const otp = generateOTP();
    const otpExpiry = calculateOTPExpiry();

    // Mark previous OTPs as used
    await OTP.updateMany(
      { userId: user._id, purpose: 'password-reset', isUsed: false },
      { isUsed: true }
    );

    // Create new OTP record
    await OTP.create({
      userId: user._id,
      email,
      code: otp,
      purpose: 'password-reset',
      expiresAt: otpExpiry
    });

    // Send OTP email
    const emailResult = await sendOTPEmail(email, user.name, otp);
    console.log('Email sending result:', emailResult);

    res.status(200).json({
      success: true,
      message: 'Password reset OTP sent to your email',
      userId: user._id
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Verify password reset OTP
 * @route   POST /api/auth/verify-reset-otp
 * @access  Public
 */
exports.verifyResetOTP = async (req, res) => {
  try {
    const { userId, otp } = req.body;

    if (!userId || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Please provide user ID and OTP'
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find OTP record
    const otpRecord = await OTP.findOne({
      userId,
      code: otp,
      purpose: 'password-reset',
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    res.status(200).json({
      success: true,
      message: 'OTP verified successfully'
    });
  } catch (error) {
    console.error('Verify reset OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Reset password with OTP
 * @route   POST /api/auth/reset-password
 * @access  Public
 */
exports.resetPassword = async (req, res) => {
  try {
    const { userId, otp, newPassword } = req.body;

    if (!userId || !otp || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Find OTP record
    const otpRecord = await OTP.findOne({
      userId,
      code: otp,
      purpose: 'password-reset',
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    // Mark OTP as used
    otpRecord.isUsed = true;
    await otpRecord.save();

    // Update password
    const userAuth = await UserAuth.findOne({ userId });
    if (!userAuth) {
      return res.status(404).json({
        success: false,
        message: 'Authentication record not found'
      });
    }

    userAuth.password = newPassword;
    await userAuth.save();

    res.status(200).json({
      success: true,
      message: 'Password reset successful'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Change password
 * @route   PUT /api/auth/change-password
 * @access  Private
 */
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Please provide current and new password'
      });
    }

    // Find auth record and include password
    const userAuth = await UserAuth.findOne({ userId: req.user.id }).select('+password');
    if (!userAuth) {
      return res.status(404).json({
        success: false,
        message: 'Authentication record not found'
      });
    }

    // Check if current password matches
    const isMatch = await userAuth.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Set new password
    userAuth.password = newPassword;
    await userAuth.save();

    res.status(200).json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

/**
 * Test email sending
 * @route   POST /api/auth/test-email
 * @access  Public
 */
exports.testEmail = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an email'
      });
    }

    // Generate test OTP
    const otp = '123456';

    // Send test email
    const emailResult = await sendOTPEmail(email, 'Test User', otp);
    console.log('Test email sending result:', emailResult);

    res.status(200).json({
      success: true,
      message: 'Test email sent. Check your inbox and server logs.',
      emailResult
    });
  } catch (error) {
    console.error('Test email error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};