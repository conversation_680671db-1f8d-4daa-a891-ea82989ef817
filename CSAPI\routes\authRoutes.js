const express = require('express');
const router = express.Router();
const {
  checkEmail,
  register,
  verifyOTP,
  resendOTP,
  login,
  getMe,
  forgotPassword,
  verifyResetOTP,
  resetPassword,
  changePassword,
  testEmail,
  googleAuth,
  googleCallback,
  githubAuth,
  githubCallback
} = require('../controllers/authController');
const { protect } = require('../middleware/authMiddleware');

// Public routes
router.post('/check-email', checkEmail);
router.post('/register', register);
router.post('/verify-otp', verifyOTP);
router.post('/resend-otp', resendOTP);
router.post('/login', login);
router.post('/forgot-password', forgotPassword);
router.post('/verify-reset-otp', verifyResetOTP);
router.post('/reset-password', resetPassword);
router.post('/test-email', testEmail);

// OAuth routes
// Google OAuth
router.get('/google', googleAuth);
router.get('/google/callback', googleCallback);

// GitHub OAuth
router.get('/github', githubAuth);
router.get('/github/callback', githubCallback);

// Protected routes
router.get('/me', protect, getMe);
router.put('/change-password', protect, changePassword);

module.exports = router;
