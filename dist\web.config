<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- Static content handling -->
    <staticContent>
      <!-- Add MIME types for modern web assets if not already defined in IIS -->
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <remove fileExtension=".map" />
      <mimeMap fileExtension=".map" mimeType="application/json" />
      <remove fileExtension=".svg" />
      <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
    </staticContent>

    <!-- Compression for better performance -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />

    <!-- Rewrite rules to handle React Router for client-side routing -->
    <rewrite>
      <rules>
        <!-- Exclude actual files and directories from being rewritten -->
        <rule name="React SPA Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <!-- Don't rewrite API requests if your API is on the same domain -->
            <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
            <!-- Don't rewrite static assets -->
            <add input="{REQUEST_URI}" pattern="^/(assets)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>

    <!-- Default document setting -->
    <defaultDocument>
      <files>
        <add value="index.html" />
      </files>
    </defaultDocument>

    <!-- Handle error pages -->
    <httpErrors errorMode="Custom">
      <remove statusCode="404" subStatusCode="-1" />
      <error statusCode="404" path="/index.html" responseMode="ExecuteURL" />
    </httpErrors>

    <!-- Security headers -->
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <!-- Adjust the Content-Security-Policy as needed for your application -->
        <!-- <add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;" /> -->
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>
