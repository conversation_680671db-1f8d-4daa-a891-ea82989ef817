const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const userAuthSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  email: {
    type: String,
    required: [true, 'Please provide an email'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email'
    ]
  },
  password: {
    type: String,
    required: function() {
      // Password is required only if not using OAuth
      return !this.googleId;
    },
    minlength: [8, 'Password must be at least 8 characters'],
    select: false // Don't return password in queries by default
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  // OAuth fields
  googleId: {
    type: String,
    unique: true,
    sparse: true // Allow null values without triggering unique constraint
  },
  githubId: {
    type: String,
    unique: true,
    sparse: true // Allow null values without triggering unique constraint
  },
  authType: {
    type: String,
    enum: ['local', 'google', 'github'],
    default: 'local'
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  lastLogin: Date,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Hash password before saving
userAuthSchema.pre('save', async function(next) {
  // Skip password hashing for OAuth users or if password hasn't changed
  if (this.googleId || this.githubId || !this.password || !this.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userAuthSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

const UserAuth = mongoose.model('UserAuth', userAuthSchema);

module.exports = UserAuth;
