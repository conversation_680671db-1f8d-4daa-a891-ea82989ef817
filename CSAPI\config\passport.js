const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const GitHubStrategy = require('passport-github2').Strategy;
const User = require('../models/User');
const UserAuth = require('../models/UserAuth');

// Configure Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      // Authorized redirect URIs
      // For local - http://localhost:3000/api/auth/google/callback
      callbackURL: '/api/auth/google/callback',
      scope: ['profile', 'email']
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Extract profile information
        const email = profile.emails[0].value;
        const name = profile.displayName;
        const googleId = profile.id;

        // Check if user already exists with this Google ID
        let userAuth = await UserAuth.findOne({ googleId });
        let user;

        if (userAuth) {
          // User exists, get user profile
          user = await User.findById(userAuth.userId);

          // Update last login time
          userAuth.lastLogin = new Date();
          await userAuth.save();
        } else {
          // Check if user exists with this email
          user = await User.findOne({ email });

          if (user) {
            // User exists with this email, check if they have auth record
            userAuth = await UserAuth.findOne({ userId: user._id });

            if (userAuth) {
              // Check if this is a local account (password-based)
              if (userAuth.authType === 'local' && !userAuth.googleId) {
                // This is a local account, now we need to link it with Google
                // Store original auth type for reference
                const originalAuthType = userAuth.authType;

                // Update existing auth record with Google ID
                userAuth.googleId = googleId;
                userAuth.authType = 'google'; // Change auth type to Google
                userAuth.isVerified = true;   // Google accounts are pre-verified
                userAuth.lastLogin = new Date();

                // Save the updated auth record
                await userAuth.save();

                // Log the account linking for audit purposes
                console.log(`Linked local account (${user.email}) with Google ID (${googleId})`);
              } else if (userAuth.googleId && userAuth.googleId !== googleId) {
                // This account is already linked to a different Google account
                // This is a security edge case - we should not allow this
                console.error(`Security alert: Attempt to link email ${email} to new Google ID ${googleId} when it's already linked to ${userAuth.googleId}`);
                return done(new Error('This email is already linked to a different Google account'), null);
              } else {
                // Account is already properly linked or is a Google account
                // Just update the last login time
                userAuth.lastLogin = new Date();
                await userAuth.save();
              }
            } else {
              // User exists but no auth record (unusual case)
              // Create new auth record for existing user
              userAuth = await UserAuth.create({
                userId: user._id,
                email,
                googleId,
                authType: 'google',
                isVerified: true
              });
            }
          } else {
            // Create new user and auth record
            user = await User.create({
              name,
              email
            });

            userAuth = await UserAuth.create({
              userId: user._id,
              email,
              googleId,
              authType: 'google',
              isVerified: true
            });
          }
        }

        // Return user object for serialization
        return done(null, user);
      } catch (error) {
        console.error('Google OAuth error:', error);
        return done(error, null);
      }
    }
  )
);

// Serialize user to session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Configure GitHub OAuth Strategy
passport.use(
  new GitHubStrategy(
    {
      clientID: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
      // Authorized redirect URIs
      // https://github.com/settings/developers visit here and add
      // For local - http://localhost:3000/api/auth/github/callback
      callbackURL: '/api/auth/github/callback',
      scope: ['user:email']
    },
    async (accessToken, refreshToken, profile, done) => {
      try {
        // Extract profile information
        // GitHub may not provide email in profile, so we need to get it from emails array
        let email;
        if (profile.emails && profile.emails.length > 0) {
          email = profile.emails[0].value;
        } else {
          // If no email is provided, we can't proceed
          return done(new Error('No email found in GitHub profile'), null);
        }

        const name = profile.displayName || profile.username;
        const githubId = profile.id;
        const githubUsername = profile.username;
        const githubProfileUrl = profile.profileUrl || `https://github.com/${githubUsername}`;

        // Check if user already exists with this GitHub ID
        let userAuth = await UserAuth.findOne({ githubId });
        let user;

        if (userAuth) {
          // User exists, get user profile
          user = await User.findById(userAuth.userId);

          // Update last login time
          userAuth.lastLogin = new Date();
          await userAuth.save();
        } else {
          // Check if user exists with this email
          user = await User.findOne({ email });

          if (user) {
            // User exists with this email, check if they have auth record
            userAuth = await UserAuth.findOne({ userId: user._id });

            if (userAuth) {
              // Check if this is a local account (password-based)
              if (userAuth.authType === 'local' && !userAuth.githubId) {
                // This is a local account, now we need to link it with GitHub

                // Update existing auth record with GitHub ID
                userAuth.githubId = githubId;
                userAuth.authType = 'github'; // Change auth type to GitHub
                userAuth.isVerified = true;   // GitHub accounts are pre-verified
                userAuth.lastLogin = new Date();

                // Save the updated auth record
                await userAuth.save();

                // Update user with GitHub profile link
                user.socialLinks = user.socialLinks || {};
                user.socialLinks.github = githubProfileUrl;
                await user.save();

                // Log the account linking for audit purposes
                console.log(`Linked local account (${user.email}) with GitHub ID (${githubId})`);
              } else if (userAuth.githubId && userAuth.githubId !== githubId) {
                // This account is already linked to a different GitHub account
                // This is a security edge case - we should not allow this
                console.error(`Security alert: Attempt to link email ${email} to new GitHub ID ${githubId} when it's already linked to ${userAuth.githubId}`);
                return done(new Error('This email is already linked to a different GitHub account'), null);
              } else {
                // Account is already properly linked or is a GitHub account
                // Just update the last login time
                userAuth.lastLogin = new Date();
                await userAuth.save();
              }
            } else {
              // User exists but no auth record (unusual case)
              // Create new auth record for existing user
              userAuth = await UserAuth.create({
                userId: user._id,
                email,
                githubId,
                authType: 'github',
                isVerified: true
              });

              // Update user with GitHub profile link
              user.socialLinks = user.socialLinks || {};
              user.socialLinks.github = githubProfileUrl;
              await user.save();
            }
          } else {
            // Create new user and auth record with GitHub profile link
            user = await User.create({
              name,
              email,
              socialLinks: {
                github: githubProfileUrl
              }
            });

            userAuth = await UserAuth.create({
              userId: user._id,
              email,
              githubId,
              authType: 'github',
              isVerified: true
            });
          }
        }

        // Return user object for serialization
        return done(null, user);
      } catch (error) {
        console.error('GitHub OAuth error:', error);
        return done(error, null);
      }
    }
  )
);

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

module.exports = passport;
