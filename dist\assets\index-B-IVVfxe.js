function Fd(l,s){for(var a=0;a<s.length;a++){const c=s[a];if(typeof c!="string"&&!Array.isArray(c)){for(const d in c)if(d!=="default"&&!(d in l)){const h=Object.getOwnPropertyDescriptor(c,d);h&&Object.defineProperty(l,d,h.get?h:{enumerable:!0,get:()=>c[d]})}}}return Object.freeze(Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}))}(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const m of h.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&c(m)}).observe(document,{childList:!0,subtree:!0});function a(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function c(d){if(d.ep)return;d.ep=!0;const h=a(d);fetch(d.href,h)}})();function ws(l){return l&&l.__esModule&&Object.prototype.hasOwnProperty.call(l,"default")?l.default:l}var ls={exports:{}},Bl={},os={exports:{}},Ne={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gc;function q0(){if(Gc)return Ne;Gc=1;var l=Symbol.for("react.element"),s=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.provider"),m=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),S=Symbol.for("react.memo"),k=Symbol.for("react.lazy"),j=Symbol.iterator;function P(y){return y===null||typeof y!="object"?null:(y=j&&y[j]||y["@@iterator"],typeof y=="function"?y:null)}var D={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,F={};function _(y,O,fe){this.props=y,this.context=O,this.refs=F,this.updater=fe||D}_.prototype.isReactComponent={},_.prototype.setState=function(y,O){if(typeof y!="object"&&typeof y!="function"&&y!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,y,O,"setState")},_.prototype.forceUpdate=function(y){this.updater.enqueueForceUpdate(this,y,"forceUpdate")};function Z(){}Z.prototype=_.prototype;function $(y,O,fe){this.props=y,this.context=O,this.refs=F,this.updater=fe||D}var K=$.prototype=new Z;K.constructor=$,U(K,_.prototype),K.isPureReactComponent=!0;var te=Array.isArray,X=Object.prototype.hasOwnProperty,ge={current:null},L={key:!0,ref:!0,__self:!0,__source:!0};function B(y,O,fe){var xe,ve={},je=null,Le=null;if(O!=null)for(xe in O.ref!==void 0&&(Le=O.ref),O.key!==void 0&&(je=""+O.key),O)X.call(O,xe)&&!L.hasOwnProperty(xe)&&(ve[xe]=O[xe]);var Re=arguments.length-2;if(Re===1)ve.children=fe;else if(1<Re){for(var Be=Array(Re),gt=0;gt<Re;gt++)Be[gt]=arguments[gt+2];ve.children=Be}if(y&&y.defaultProps)for(xe in Re=y.defaultProps,Re)ve[xe]===void 0&&(ve[xe]=Re[xe]);return{$$typeof:l,type:y,key:je,ref:Le,props:ve,_owner:ge.current}}function q(y,O){return{$$typeof:l,type:y.type,key:O,ref:y.ref,props:y.props,_owner:y._owner}}function Y(y){return typeof y=="object"&&y!==null&&y.$$typeof===l}function Se(y){var O={"=":"=0",":":"=2"};return"$"+y.replace(/[=:]/g,function(fe){return O[fe]})}var ke=/\/+/g;function Ce(y,O){return typeof y=="object"&&y!==null&&y.key!=null?Se(""+y.key):O.toString(36)}function Te(y,O,fe,xe,ve){var je=typeof y;(je==="undefined"||je==="boolean")&&(y=null);var Le=!1;if(y===null)Le=!0;else switch(je){case"string":case"number":Le=!0;break;case"object":switch(y.$$typeof){case l:case s:Le=!0}}if(Le)return Le=y,ve=ve(Le),y=xe===""?"."+Ce(Le,0):xe,te(ve)?(fe="",y!=null&&(fe=y.replace(ke,"$&/")+"/"),Te(ve,O,fe,"",function(gt){return gt})):ve!=null&&(Y(ve)&&(ve=q(ve,fe+(!ve.key||Le&&Le.key===ve.key?"":(""+ve.key).replace(ke,"$&/")+"/")+y)),O.push(ve)),1;if(Le=0,xe=xe===""?".":xe+":",te(y))for(var Re=0;Re<y.length;Re++){je=y[Re];var Be=xe+Ce(je,Re);Le+=Te(je,O,fe,Be,ve)}else if(Be=P(y),typeof Be=="function")for(y=Be.call(y),Re=0;!(je=y.next()).done;)je=je.value,Be=xe+Ce(je,Re++),Le+=Te(je,O,fe,Be,ve);else if(je==="object")throw O=String(y),Error("Objects are not valid as a React child (found: "+(O==="[object Object]"?"object with keys {"+Object.keys(y).join(", ")+"}":O)+"). If you meant to render a collection of children, use an array instead.");return Le}function We(y,O,fe){if(y==null)return y;var xe=[],ve=0;return Te(y,xe,"","",function(je){return O.call(fe,je,ve++)}),xe}function Ke(y){if(y._status===-1){var O=y._result;O=O(),O.then(function(fe){(y._status===0||y._status===-1)&&(y._status=1,y._result=fe)},function(fe){(y._status===0||y._status===-1)&&(y._status=2,y._result=fe)}),y._status===-1&&(y._status=0,y._result=O)}if(y._status===1)return y._result.default;throw y._result}var ue={current:null},M={transition:null},J={ReactCurrentDispatcher:ue,ReactCurrentBatchConfig:M,ReactCurrentOwner:ge};function I(){throw Error("act(...) is not supported in production builds of React.")}return Ne.Children={map:We,forEach:function(y,O,fe){We(y,function(){O.apply(this,arguments)},fe)},count:function(y){var O=0;return We(y,function(){O++}),O},toArray:function(y){return We(y,function(O){return O})||[]},only:function(y){if(!Y(y))throw Error("React.Children.only expected to receive a single React element child.");return y}},Ne.Component=_,Ne.Fragment=a,Ne.Profiler=d,Ne.PureComponent=$,Ne.StrictMode=c,Ne.Suspense=p,Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=J,Ne.act=I,Ne.cloneElement=function(y,O,fe){if(y==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+y+".");var xe=U({},y.props),ve=y.key,je=y.ref,Le=y._owner;if(O!=null){if(O.ref!==void 0&&(je=O.ref,Le=ge.current),O.key!==void 0&&(ve=""+O.key),y.type&&y.type.defaultProps)var Re=y.type.defaultProps;for(Be in O)X.call(O,Be)&&!L.hasOwnProperty(Be)&&(xe[Be]=O[Be]===void 0&&Re!==void 0?Re[Be]:O[Be])}var Be=arguments.length-2;if(Be===1)xe.children=fe;else if(1<Be){Re=Array(Be);for(var gt=0;gt<Be;gt++)Re[gt]=arguments[gt+2];xe.children=Re}return{$$typeof:l,type:y.type,key:ve,ref:je,props:xe,_owner:Le}},Ne.createContext=function(y){return y={$$typeof:m,_currentValue:y,_currentValue2:y,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},y.Provider={$$typeof:h,_context:y},y.Consumer=y},Ne.createElement=B,Ne.createFactory=function(y){var O=B.bind(null,y);return O.type=y,O},Ne.createRef=function(){return{current:null}},Ne.forwardRef=function(y){return{$$typeof:v,render:y}},Ne.isValidElement=Y,Ne.lazy=function(y){return{$$typeof:k,_payload:{_status:-1,_result:y},_init:Ke}},Ne.memo=function(y,O){return{$$typeof:S,type:y,compare:O===void 0?null:O}},Ne.startTransition=function(y){var O=M.transition;M.transition={};try{y()}finally{M.transition=O}},Ne.unstable_act=I,Ne.useCallback=function(y,O){return ue.current.useCallback(y,O)},Ne.useContext=function(y){return ue.current.useContext(y)},Ne.useDebugValue=function(){},Ne.useDeferredValue=function(y){return ue.current.useDeferredValue(y)},Ne.useEffect=function(y,O){return ue.current.useEffect(y,O)},Ne.useId=function(){return ue.current.useId()},Ne.useImperativeHandle=function(y,O,fe){return ue.current.useImperativeHandle(y,O,fe)},Ne.useInsertionEffect=function(y,O){return ue.current.useInsertionEffect(y,O)},Ne.useLayoutEffect=function(y,O){return ue.current.useLayoutEffect(y,O)},Ne.useMemo=function(y,O){return ue.current.useMemo(y,O)},Ne.useReducer=function(y,O,fe){return ue.current.useReducer(y,O,fe)},Ne.useRef=function(y){return ue.current.useRef(y)},Ne.useState=function(y){return ue.current.useState(y)},Ne.useSyncExternalStore=function(y,O,fe){return ue.current.useSyncExternalStore(y,O,fe)},Ne.useTransition=function(){return ue.current.useTransition()},Ne.version="18.3.1",Ne}var Jc;function Gl(){return Jc||(Jc=1,os.exports=q0()),os.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xc;function Z0(){if(Xc)return Bl;Xc=1;var l=Gl(),s=Symbol.for("react.element"),a=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,d=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,h={key:!0,ref:!0,__self:!0,__source:!0};function m(v,p,S){var k,j={},P=null,D=null;S!==void 0&&(P=""+S),p.key!==void 0&&(P=""+p.key),p.ref!==void 0&&(D=p.ref);for(k in p)c.call(p,k)&&!h.hasOwnProperty(k)&&(j[k]=p[k]);if(v&&v.defaultProps)for(k in p=v.defaultProps,p)j[k]===void 0&&(j[k]=p[k]);return{$$typeof:s,type:v,key:P,ref:D,props:j,_owner:d.current}}return Bl.Fragment=a,Bl.jsx=m,Bl.jsxs=m,Bl}var qc;function eh(){return qc||(qc=1,ls.exports=Z0()),ls.exports}var i=eh(),C=Gl();const vr=ws(C),th=Fd({__proto__:null,default:vr},[C]);var ui={},is={exports:{}},Nt={},as={exports:{}},ss={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zc;function rh(){return Zc||(Zc=1,function(l){function s(M,J){var I=M.length;M.push(J);e:for(;0<I;){var y=I-1>>>1,O=M[y];if(0<d(O,J))M[y]=J,M[I]=O,I=y;else break e}}function a(M){return M.length===0?null:M[0]}function c(M){if(M.length===0)return null;var J=M[0],I=M.pop();if(I!==J){M[0]=I;e:for(var y=0,O=M.length,fe=O>>>1;y<fe;){var xe=2*(y+1)-1,ve=M[xe],je=xe+1,Le=M[je];if(0>d(ve,I))je<O&&0>d(Le,ve)?(M[y]=Le,M[je]=I,y=je):(M[y]=ve,M[xe]=I,y=xe);else if(je<O&&0>d(Le,I))M[y]=Le,M[je]=I,y=je;else break e}}return J}function d(M,J){var I=M.sortIndex-J.sortIndex;return I!==0?I:M.id-J.id}if(typeof performance=="object"&&typeof performance.now=="function"){var h=performance;l.unstable_now=function(){return h.now()}}else{var m=Date,v=m.now();l.unstable_now=function(){return m.now()-v}}var p=[],S=[],k=1,j=null,P=3,D=!1,U=!1,F=!1,_=typeof setTimeout=="function"?setTimeout:null,Z=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function K(M){for(var J=a(S);J!==null;){if(J.callback===null)c(S);else if(J.startTime<=M)c(S),J.sortIndex=J.expirationTime,s(p,J);else break;J=a(S)}}function te(M){if(F=!1,K(M),!U)if(a(p)!==null)U=!0,Ke(X);else{var J=a(S);J!==null&&ue(te,J.startTime-M)}}function X(M,J){U=!1,F&&(F=!1,Z(B),B=-1),D=!0;var I=P;try{for(K(J),j=a(p);j!==null&&(!(j.expirationTime>J)||M&&!Se());){var y=j.callback;if(typeof y=="function"){j.callback=null,P=j.priorityLevel;var O=y(j.expirationTime<=J);J=l.unstable_now(),typeof O=="function"?j.callback=O:j===a(p)&&c(p),K(J)}else c(p);j=a(p)}if(j!==null)var fe=!0;else{var xe=a(S);xe!==null&&ue(te,xe.startTime-J),fe=!1}return fe}finally{j=null,P=I,D=!1}}var ge=!1,L=null,B=-1,q=5,Y=-1;function Se(){return!(l.unstable_now()-Y<q)}function ke(){if(L!==null){var M=l.unstable_now();Y=M;var J=!0;try{J=L(!0,M)}finally{J?Ce():(ge=!1,L=null)}}else ge=!1}var Ce;if(typeof $=="function")Ce=function(){$(ke)};else if(typeof MessageChannel<"u"){var Te=new MessageChannel,We=Te.port2;Te.port1.onmessage=ke,Ce=function(){We.postMessage(null)}}else Ce=function(){_(ke,0)};function Ke(M){L=M,ge||(ge=!0,Ce())}function ue(M,J){B=_(function(){M(l.unstable_now())},J)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(M){M.callback=null},l.unstable_continueExecution=function(){U||D||(U=!0,Ke(X))},l.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<M?Math.floor(1e3/M):5},l.unstable_getCurrentPriorityLevel=function(){return P},l.unstable_getFirstCallbackNode=function(){return a(p)},l.unstable_next=function(M){switch(P){case 1:case 2:case 3:var J=3;break;default:J=P}var I=P;P=J;try{return M()}finally{P=I}},l.unstable_pauseExecution=function(){},l.unstable_requestPaint=function(){},l.unstable_runWithPriority=function(M,J){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var I=P;P=M;try{return J()}finally{P=I}},l.unstable_scheduleCallback=function(M,J,I){var y=l.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?y+I:y):I=y,M){case 1:var O=-1;break;case 2:O=250;break;case 5:O=**********;break;case 4:O=1e4;break;default:O=5e3}return O=I+O,M={id:k++,callback:J,priorityLevel:M,startTime:I,expirationTime:O,sortIndex:-1},I>y?(M.sortIndex=I,s(S,M),a(p)===null&&M===a(S)&&(F?(Z(B),B=-1):F=!0,ue(te,I-y))):(M.sortIndex=O,s(p,M),U||D||(U=!0,Ke(X))),M},l.unstable_shouldYield=Se,l.unstable_wrapCallback=function(M){var J=P;return function(){var I=P;P=J;try{return M.apply(this,arguments)}finally{P=I}}}}(ss)),ss}var ed;function nh(){return ed||(ed=1,as.exports=rh()),as.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var td;function lh(){if(td)return Nt;td=1;var l=Gl(),s=nh();function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var c=new Set,d={};function h(e,t){m(e,t),m(e+"Capture",t)}function m(e,t){for(d[e]=t,e=0;e<t.length;e++)c.add(t[e])}var v=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,S=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,k={},j={};function P(e){return p.call(j,e)?!0:p.call(k,e)?!1:S.test(e)?j[e]=!0:(k[e]=!0,!1)}function D(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function U(e,t,r,n){if(t===null||typeof t>"u"||D(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function F(e,t,r,n,o,u,f){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=o,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=u,this.removeEmptyString=f}var _={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){_[e]=new F(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];_[t]=new F(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){_[e]=new F(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){_[e]=new F(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){_[e]=new F(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){_[e]=new F(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){_[e]=new F(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){_[e]=new F(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){_[e]=new F(e,5,!1,e.toLowerCase(),null,!1,!1)});var Z=/[\-:]([a-z])/g;function $(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Z,$);_[t]=new F(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Z,$);_[t]=new F(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Z,$);_[t]=new F(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){_[e]=new F(e,1,!1,e.toLowerCase(),null,!1,!1)}),_.xlinkHref=new F("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){_[e]=new F(e,1,!1,e.toLowerCase(),null,!0,!0)});function K(e,t,r,n){var o=_.hasOwnProperty(t)?_[t]:null;(o!==null?o.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(U(t,r,o,n)&&(r=null),n||o===null?P(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):o.mustUseProperty?e[o.propertyName]=r===null?o.type===3?!1:"":r:(t=o.attributeName,n=o.attributeNamespace,r===null?e.removeAttribute(t):(o=o.type,r=o===3||o===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var te=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,X=Symbol.for("react.element"),ge=Symbol.for("react.portal"),L=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),q=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),Se=Symbol.for("react.context"),ke=Symbol.for("react.forward_ref"),Ce=Symbol.for("react.suspense"),Te=Symbol.for("react.suspense_list"),We=Symbol.for("react.memo"),Ke=Symbol.for("react.lazy"),ue=Symbol.for("react.offscreen"),M=Symbol.iterator;function J(e){return e===null||typeof e!="object"?null:(e=M&&e[M]||e["@@iterator"],typeof e=="function"?e:null)}var I=Object.assign,y;function O(e){if(y===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);y=t&&t[1]||""}return`
`+y+e}var fe=!1;function xe(e,t){if(!e||fe)return"";fe=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(T){var n=T}Reflect.construct(e,[],t)}else{try{t.call()}catch(T){n=T}e.call(t.prototype)}else{try{throw Error()}catch(T){n=T}e()}}catch(T){if(T&&n&&typeof T.stack=="string"){for(var o=T.stack.split(`
`),u=n.stack.split(`
`),f=o.length-1,g=u.length-1;1<=f&&0<=g&&o[f]!==u[g];)g--;for(;1<=f&&0<=g;f--,g--)if(o[f]!==u[g]){if(f!==1||g!==1)do if(f--,g--,0>g||o[f]!==u[g]){var w=`
`+o[f].replace(" at new "," at ");return e.displayName&&w.includes("<anonymous>")&&(w=w.replace("<anonymous>",e.displayName)),w}while(1<=f&&0<=g);break}}}finally{fe=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?O(e):""}function ve(e){switch(e.tag){case 5:return O(e.type);case 16:return O("Lazy");case 13:return O("Suspense");case 19:return O("SuspenseList");case 0:case 2:case 15:return e=xe(e.type,!1),e;case 11:return e=xe(e.type.render,!1),e;case 1:return e=xe(e.type,!0),e;default:return""}}function je(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case L:return"Fragment";case ge:return"Portal";case q:return"Profiler";case B:return"StrictMode";case Ce:return"Suspense";case Te:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Se:return(e.displayName||"Context")+".Consumer";case Y:return(e._context.displayName||"Context")+".Provider";case ke:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case We:return t=e.displayName||null,t!==null?t:je(e.type)||"Memo";case Ke:t=e._payload,e=e._init;try{return je(e(t))}catch{}}return null}function Le(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return je(t);case 8:return t===B?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Re(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Be(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gt(e){var t=Be(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var o=r.get,u=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(f){n=""+f,u.call(this,f)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(f){n=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xn(e){e._valueTracker||(e._valueTracker=gt(e))}function ot(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=Be(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function Ut(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function wn(e,t){var r=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Xl(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=Re(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Gt(e,t){t=t.checked,t!=null&&K(e,"checked",t,!1)}function el(e,t){Gt(e,t);var r=Re(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?jn(e,t.type,r):t.hasOwnProperty("defaultValue")&&jn(e,t.type,Re(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ql(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function jn(e,t,r){(t!=="number"||Ut(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Jr=Array.isArray;function wr(e,t,r,n){if(e=e.options,t){t={};for(var o=0;o<r.length;o++)t["$"+r[o]]=!0;for(r=0;r<e.length;r++)o=t.hasOwnProperty("$"+e[r].value),e[r].selected!==o&&(e[r].selected=o),o&&n&&(e[r].defaultSelected=!0)}else{for(r=""+Re(r),t=null,o=0;o<e.length;o++){if(e[o].value===r){e[o].selected=!0,n&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function tl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(a(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Zl(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(a(92));if(Jr(r)){if(1<r.length)throw Error(a(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Re(r)}}function Jt(e,t){var r=Re(t.value),n=Re(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function jr(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function rl(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xr(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?rl(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Pt,Tt=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,o){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Pt=Pt||document.createElement("div"),Pt.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Pt.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Sr(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Xt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ki=["Webkit","ms","Moz","O"];Object.keys(Xt).forEach(function(e){ki.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Xt[t]=Xt[e]})});function At(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Xt.hasOwnProperty(e)&&Xt[e]?(""+t).trim():t+"px"}function nl(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,o=At(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,o):e[r]=o}}var eo=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Sn(e,t){if(t){if(eo[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(a(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(a(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(t.style!=null&&typeof t.style!="object")throw Error(a(62))}}function ll(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var kn=null;function kr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Nn=null,qt=null,Zt=null;function to(e){if(e=Pl(e)){if(typeof Nn!="function")throw Error(a(280));var t=e.stateNode;t&&(t=No(t),Nn(e.stateNode,e.type,t))}}function ol(e){qt?Zt?Zt.push(e):Zt=[e]:qt=e}function ro(){if(qt){var e=qt,t=Zt;if(Zt=qt=null,to(e),t)for(e=0;e<t.length;e++)to(t[e])}}function il(e,t){return e(t)}function qr(){}var Nr=!1;function no(e,t,r){if(Nr)return e(t,r);Nr=!0;try{return il(e,t,r)}finally{Nr=!1,(qt!==null||Zt!==null)&&(qr(),ro())}}function Zr(e,t){var r=e.stateNode;if(r===null)return null;var n=No(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(a(231,t,typeof r));return r}var x=!1;if(v)try{var E={};Object.defineProperty(E,"passive",{get:function(){x=!0}}),window.addEventListener("test",E,E),window.removeEventListener("test",E,E)}catch{x=!1}function z(e,t,r,n,o,u,f,g,w){var T=Array.prototype.slice.call(arguments,3);try{t.apply(r,T)}catch(W){this.onError(W)}}var A=!1,G=null,de=!1,pe=null,oe={onError:function(e){A=!0,G=e}};function ae(e,t,r,n,o,u,f,g,w){A=!1,G=null,z.apply(oe,arguments)}function re(e,t,r,n,o,u,f,g,w){if(ae.apply(this,arguments),A){if(A){var T=G;A=!1,G=null}else throw Error(a(198));de||(de=!0,pe=T)}}function le(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function we(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ee(e){if(le(e)!==e)throw Error(a(188))}function rt(e){var t=e.alternate;if(!t){if(t=le(e),t===null)throw Error(a(188));return t!==e?null:e}for(var r=e,n=t;;){var o=r.return;if(o===null)break;var u=o.alternate;if(u===null){if(n=o.return,n!==null){r=n;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===r)return Ee(o),e;if(u===n)return Ee(o),t;u=u.sibling}throw Error(a(188))}if(r.return!==n.return)r=o,n=u;else{for(var f=!1,g=o.child;g;){if(g===r){f=!0,r=o,n=u;break}if(g===n){f=!0,n=o,r=u;break}g=g.sibling}if(!f){for(g=u.child;g;){if(g===r){f=!0,r=u,n=o;break}if(g===n){f=!0,n=u,r=o;break}g=g.sibling}if(!f)throw Error(a(189))}}if(r.alternate!==n)throw Error(a(190))}if(r.tag!==3)throw Error(a(188));return r.stateNode.current===r?e:t}function Fe(e){return e=rt(e),e!==null?_e(e):null}function _e(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=_e(e);if(t!==null)return t;e=e.sibling}return null}var He=s.unstable_scheduleCallback,ir=s.unstable_cancelCallback,en=s.unstable_shouldYield,Bt=s.unstable_requestPaint,ze=s.unstable_now,al=s.unstable_getCurrentPriorityLevel,ar=s.unstable_ImmediatePriority,er=s.unstable_UserBlockingPriority,sr=s.unstable_NormalPriority,En=s.unstable_LowPriority,be=s.unstable_IdlePriority,Ae=null,ut=null;function tn(e){if(ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(Ae,e,void 0,(e.current.flags&128)===128)}catch{}}var De=Math.clz32?Math.clz32:Ni,Er=Math.log,lo=Math.LN2;function Ni(e){return e>>>=0,e===0?32:31-(Er(e)/lo|0)|0}var oo=64,io=4194304;function sl(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ao(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,o=e.suspendedLanes,u=e.pingedLanes,f=r&268435455;if(f!==0){var g=f&~o;g!==0?n=sl(g):(u&=f,u!==0&&(n=sl(u)))}else f=r&~o,f!==0?n=sl(f):u!==0&&(n=sl(u));if(n===0)return 0;if(t!==0&&t!==n&&(t&o)===0&&(o=n&-n,u=t&-t,o>=u||o===16&&(u&4194240)!==0))return t;if((n&4)!==0&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-De(t),o=1<<r,n|=e[r],t&=~o;return n}function vf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function yf(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,o=e.expirationTimes,u=e.pendingLanes;0<u;){var f=31-De(u),g=1<<f,w=o[f];w===-1?((g&r)===0||(g&n)!==0)&&(o[f]=vf(g,t)):w<=t&&(e.expiredLanes|=g),u&=~g}}function Ei(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ps(){var e=oo;return oo<<=1,(oo&4194240)===0&&(oo=64),e}function Pi(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function ul(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-De(t),e[t]=r}function xf(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var o=31-De(r),u=1<<o;t[o]=0,n[o]=-1,e[o]=-1,r&=~u}}function Ci(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-De(r),o=1<<n;o&t|e[n]&t&&(e[n]|=t),r&=~o}}var Ie=0;function Cs(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var bs,bi,Ls,Rs,_s,Li=!1,so=[],Pr=null,Cr=null,br=null,cl=new Map,dl=new Map,Lr=[],wf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ts(e,t){switch(e){case"focusin":case"focusout":Pr=null;break;case"dragenter":case"dragleave":Cr=null;break;case"mouseover":case"mouseout":br=null;break;case"pointerover":case"pointerout":cl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":dl.delete(t.pointerId)}}function fl(e,t,r,n,o,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:u,targetContainers:[o]},t!==null&&(t=Pl(t),t!==null&&bi(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function jf(e,t,r,n,o){switch(t){case"focusin":return Pr=fl(Pr,e,t,r,n,o),!0;case"dragenter":return Cr=fl(Cr,e,t,r,n,o),!0;case"mouseover":return br=fl(br,e,t,r,n,o),!0;case"pointerover":var u=o.pointerId;return cl.set(u,fl(cl.get(u)||null,e,t,r,n,o)),!0;case"gotpointercapture":return u=o.pointerId,dl.set(u,fl(dl.get(u)||null,e,t,r,n,o)),!0}return!1}function Ms(e){var t=rn(e.target);if(t!==null){var r=le(t);if(r!==null){if(t=r.tag,t===13){if(t=we(r),t!==null){e.blockedOn=t,_s(e.priority,function(){Ls(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function uo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=_i(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);kn=n,r.target.dispatchEvent(n),kn=null}else return t=Pl(r),t!==null&&bi(t),e.blockedOn=r,!1;t.shift()}return!0}function zs(e,t,r){uo(e)&&r.delete(t)}function Sf(){Li=!1,Pr!==null&&uo(Pr)&&(Pr=null),Cr!==null&&uo(Cr)&&(Cr=null),br!==null&&uo(br)&&(br=null),cl.forEach(zs),dl.forEach(zs)}function hl(e,t){e.blockedOn===t&&(e.blockedOn=null,Li||(Li=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,Sf)))}function ml(e){function t(o){return hl(o,e)}if(0<so.length){hl(so[0],e);for(var r=1;r<so.length;r++){var n=so[r];n.blockedOn===e&&(n.blockedOn=null)}}for(Pr!==null&&hl(Pr,e),Cr!==null&&hl(Cr,e),br!==null&&hl(br,e),cl.forEach(t),dl.forEach(t),r=0;r<Lr.length;r++)n=Lr[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Lr.length&&(r=Lr[0],r.blockedOn===null);)Ms(r),r.blockedOn===null&&Lr.shift()}var Pn=te.ReactCurrentBatchConfig,co=!0;function kf(e,t,r,n){var o=Ie,u=Pn.transition;Pn.transition=null;try{Ie=1,Ri(e,t,r,n)}finally{Ie=o,Pn.transition=u}}function Nf(e,t,r,n){var o=Ie,u=Pn.transition;Pn.transition=null;try{Ie=4,Ri(e,t,r,n)}finally{Ie=o,Pn.transition=u}}function Ri(e,t,r,n){if(co){var o=_i(e,t,r,n);if(o===null)Yi(e,t,n,fo,r),Ts(e,n);else if(jf(o,e,t,r,n))n.stopPropagation();else if(Ts(e,n),t&4&&-1<wf.indexOf(e)){for(;o!==null;){var u=Pl(o);if(u!==null&&bs(u),u=_i(e,t,r,n),u===null&&Yi(e,t,n,fo,r),u===o)break;o=u}o!==null&&n.stopPropagation()}else Yi(e,t,n,null,r)}}var fo=null;function _i(e,t,r,n){if(fo=null,e=kr(n),e=rn(e),e!==null)if(t=le(e),t===null)e=null;else if(r=t.tag,r===13){if(e=we(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return fo=e,null}function Ds(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(al()){case ar:return 1;case er:return 4;case sr:case En:return 16;case be:return 536870912;default:return 16}default:return 16}}var Rr=null,Ti=null,ho=null;function Os(){if(ho)return ho;var e,t=Ti,r=t.length,n,o="value"in Rr?Rr.value:Rr.textContent,u=o.length;for(e=0;e<r&&t[e]===o[e];e++);var f=r-e;for(n=1;n<=f&&t[r-n]===o[u-n];n++);return ho=o.slice(e,1<n?1-n:void 0)}function mo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function po(){return!0}function Fs(){return!1}function Ct(e){function t(r,n,o,u,f){this._reactName=r,this._targetInst=o,this.type=n,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var g in e)e.hasOwnProperty(g)&&(r=e[g],this[g]=r?r(u):u[g]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?po:Fs,this.isPropagationStopped=Fs,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=po)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=po)},persist:function(){},isPersistent:po}),t}var Cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Mi=Ct(Cn),pl=I({},Cn,{view:0,detail:0}),Ef=Ct(pl),zi,Di,gl,go=I({},pl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==gl&&(gl&&e.type==="mousemove"?(zi=e.screenX-gl.screenX,Di=e.screenY-gl.screenY):Di=zi=0,gl=e),zi)},movementY:function(e){return"movementY"in e?e.movementY:Di}}),Is=Ct(go),Pf=I({},go,{dataTransfer:0}),Cf=Ct(Pf),bf=I({},pl,{relatedTarget:0}),Oi=Ct(bf),Lf=I({},Cn,{animationName:0,elapsedTime:0,pseudoElement:0}),Rf=Ct(Lf),_f=I({},Cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Tf=Ct(_f),Mf=I({},Cn,{data:0}),Us=Ct(Mf),zf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Df={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Of={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ff(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Of[e])?!!t[e]:!1}function Fi(){return Ff}var If=I({},pl,{key:function(e){if(e.key){var t=zf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=mo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Df[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fi,charCode:function(e){return e.type==="keypress"?mo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?mo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Uf=Ct(If),Af=I({},go,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),As=Ct(Af),Bf=I({},pl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fi}),Vf=Ct(Bf),Wf=I({},Cn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Hf=Ct(Wf),$f=I({},go,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Qf=Ct($f),Kf=[9,13,27,32],Ii=v&&"CompositionEvent"in window,vl=null;v&&"documentMode"in document&&(vl=document.documentMode);var Yf=v&&"TextEvent"in window&&!vl,Bs=v&&(!Ii||vl&&8<vl&&11>=vl),Vs=" ",Ws=!1;function Hs(e,t){switch(e){case"keyup":return Kf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $s(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var bn=!1;function Gf(e,t){switch(e){case"compositionend":return $s(t);case"keypress":return t.which!==32?null:(Ws=!0,Vs);case"textInput":return e=t.data,e===Vs&&Ws?null:e;default:return null}}function Jf(e,t){if(bn)return e==="compositionend"||!Ii&&Hs(e,t)?(e=Os(),ho=Ti=Rr=null,bn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Bs&&t.locale!=="ko"?null:t.data;default:return null}}var Xf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Xf[e.type]:t==="textarea"}function Ks(e,t,r,n){ol(n),t=jo(t,"onChange"),0<t.length&&(r=new Mi("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var yl=null,xl=null;function qf(e){du(e,0)}function vo(e){var t=Mn(e);if(ot(t))return e}function Zf(e,t){if(e==="change")return t}var Ys=!1;if(v){var Ui;if(v){var Ai="oninput"in document;if(!Ai){var Gs=document.createElement("div");Gs.setAttribute("oninput","return;"),Ai=typeof Gs.oninput=="function"}Ui=Ai}else Ui=!1;Ys=Ui&&(!document.documentMode||9<document.documentMode)}function Js(){yl&&(yl.detachEvent("onpropertychange",Xs),xl=yl=null)}function Xs(e){if(e.propertyName==="value"&&vo(xl)){var t=[];Ks(t,xl,e,kr(e)),no(qf,t)}}function e0(e,t,r){e==="focusin"?(Js(),yl=t,xl=r,yl.attachEvent("onpropertychange",Xs)):e==="focusout"&&Js()}function t0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return vo(xl)}function r0(e,t){if(e==="click")return vo(t)}function n0(e,t){if(e==="input"||e==="change")return vo(t)}function l0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Vt=typeof Object.is=="function"?Object.is:l0;function wl(e,t){if(Vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var o=r[n];if(!p.call(t,o)||!Vt(e[o],t[o]))return!1}return!0}function qs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zs(e,t){var r=qs(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=qs(r)}}function eu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?eu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function tu(){for(var e=window,t=Ut();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=Ut(e.document)}return t}function Bi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function o0(e){var t=tu(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&eu(r.ownerDocument.documentElement,r)){if(n!==null&&Bi(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=r.textContent.length,u=Math.min(n.start,o);n=n.end===void 0?u:Math.min(n.end,o),!e.extend&&u>n&&(o=n,n=u,u=o),o=Zs(r,u);var f=Zs(r,n);o&&f&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==f.node||e.focusOffset!==f.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),u>n?(e.addRange(t),e.extend(f.node,f.offset)):(t.setEnd(f.node,f.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var i0=v&&"documentMode"in document&&11>=document.documentMode,Ln=null,Vi=null,jl=null,Wi=!1;function ru(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Wi||Ln==null||Ln!==Ut(n)||(n=Ln,"selectionStart"in n&&Bi(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),jl&&wl(jl,n)||(jl=n,n=jo(Vi,"onSelect"),0<n.length&&(t=new Mi("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Ln)))}function yo(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Rn={animationend:yo("Animation","AnimationEnd"),animationiteration:yo("Animation","AnimationIteration"),animationstart:yo("Animation","AnimationStart"),transitionend:yo("Transition","TransitionEnd")},Hi={},nu={};v&&(nu=document.createElement("div").style,"AnimationEvent"in window||(delete Rn.animationend.animation,delete Rn.animationiteration.animation,delete Rn.animationstart.animation),"TransitionEvent"in window||delete Rn.transitionend.transition);function xo(e){if(Hi[e])return Hi[e];if(!Rn[e])return e;var t=Rn[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in nu)return Hi[e]=t[r];return e}var lu=xo("animationend"),ou=xo("animationiteration"),iu=xo("animationstart"),au=xo("transitionend"),su=new Map,uu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _r(e,t){su.set(e,t),h(t,[e])}for(var $i=0;$i<uu.length;$i++){var Qi=uu[$i],a0=Qi.toLowerCase(),s0=Qi[0].toUpperCase()+Qi.slice(1);_r(a0,"on"+s0)}_r(lu,"onAnimationEnd"),_r(ou,"onAnimationIteration"),_r(iu,"onAnimationStart"),_r("dblclick","onDoubleClick"),_r("focusin","onFocus"),_r("focusout","onBlur"),_r(au,"onTransitionEnd"),m("onMouseEnter",["mouseout","mouseover"]),m("onMouseLeave",["mouseout","mouseover"]),m("onPointerEnter",["pointerout","pointerover"]),m("onPointerLeave",["pointerout","pointerover"]),h("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),h("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),h("onBeforeInput",["compositionend","keypress","textInput","paste"]),h("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),h("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),h("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Sl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),u0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Sl));function cu(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,re(n,t,void 0,e),e.currentTarget=null}function du(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],o=n.event;n=n.listeners;e:{var u=void 0;if(t)for(var f=n.length-1;0<=f;f--){var g=n[f],w=g.instance,T=g.currentTarget;if(g=g.listener,w!==u&&o.isPropagationStopped())break e;cu(o,g,T),u=w}else for(f=0;f<n.length;f++){if(g=n[f],w=g.instance,T=g.currentTarget,g=g.listener,w!==u&&o.isPropagationStopped())break e;cu(o,g,T),u=w}}}if(de)throw e=pe,de=!1,pe=null,e}function $e(e,t){var r=t[ea];r===void 0&&(r=t[ea]=new Set);var n=e+"__bubble";r.has(n)||(fu(t,e,2,!1),r.add(n))}function Ki(e,t,r){var n=0;t&&(n|=4),fu(r,e,n,t)}var wo="_reactListening"+Math.random().toString(36).slice(2);function kl(e){if(!e[wo]){e[wo]=!0,c.forEach(function(r){r!=="selectionchange"&&(u0.has(r)||Ki(r,!1,e),Ki(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[wo]||(t[wo]=!0,Ki("selectionchange",!1,t))}}function fu(e,t,r,n){switch(Ds(t)){case 1:var o=kf;break;case 4:o=Nf;break;default:o=Ri}r=o.bind(null,t,r,e),o=void 0,!x||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),n?o!==void 0?e.addEventListener(t,r,{capture:!0,passive:o}):e.addEventListener(t,r,!0):o!==void 0?e.addEventListener(t,r,{passive:o}):e.addEventListener(t,r,!1)}function Yi(e,t,r,n,o){var u=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var f=n.tag;if(f===3||f===4){var g=n.stateNode.containerInfo;if(g===o||g.nodeType===8&&g.parentNode===o)break;if(f===4)for(f=n.return;f!==null;){var w=f.tag;if((w===3||w===4)&&(w=f.stateNode.containerInfo,w===o||w.nodeType===8&&w.parentNode===o))return;f=f.return}for(;g!==null;){if(f=rn(g),f===null)return;if(w=f.tag,w===5||w===6){n=u=f;continue e}g=g.parentNode}}n=n.return}no(function(){var T=u,W=kr(r),H=[];e:{var V=su.get(e);if(V!==void 0){var ee=Mi,ie=e;switch(e){case"keypress":if(mo(r)===0)break e;case"keydown":case"keyup":ee=Uf;break;case"focusin":ie="focus",ee=Oi;break;case"focusout":ie="blur",ee=Oi;break;case"beforeblur":case"afterblur":ee=Oi;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ee=Is;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ee=Cf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ee=Vf;break;case lu:case ou:case iu:ee=Rf;break;case au:ee=Hf;break;case"scroll":ee=Ef;break;case"wheel":ee=Qf;break;case"copy":case"cut":case"paste":ee=Tf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ee=As}var se=(t&4)!==0,Ze=!se&&e==="scroll",b=se?V!==null?V+"Capture":null:V;se=[];for(var N=T,R;N!==null;){R=N;var Q=R.stateNode;if(R.tag===5&&Q!==null&&(R=Q,b!==null&&(Q=Zr(N,b),Q!=null&&se.push(Nl(N,Q,R)))),Ze)break;N=N.return}0<se.length&&(V=new ee(V,ie,null,r,W),H.push({event:V,listeners:se}))}}if((t&7)===0){e:{if(V=e==="mouseover"||e==="pointerover",ee=e==="mouseout"||e==="pointerout",V&&r!==kn&&(ie=r.relatedTarget||r.fromElement)&&(rn(ie)||ie[ur]))break e;if((ee||V)&&(V=W.window===W?W:(V=W.ownerDocument)?V.defaultView||V.parentWindow:window,ee?(ie=r.relatedTarget||r.toElement,ee=T,ie=ie?rn(ie):null,ie!==null&&(Ze=le(ie),ie!==Ze||ie.tag!==5&&ie.tag!==6)&&(ie=null)):(ee=null,ie=T),ee!==ie)){if(se=Is,Q="onMouseLeave",b="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(se=As,Q="onPointerLeave",b="onPointerEnter",N="pointer"),Ze=ee==null?V:Mn(ee),R=ie==null?V:Mn(ie),V=new se(Q,N+"leave",ee,r,W),V.target=Ze,V.relatedTarget=R,Q=null,rn(W)===T&&(se=new se(b,N+"enter",ie,r,W),se.target=R,se.relatedTarget=Ze,Q=se),Ze=Q,ee&&ie)t:{for(se=ee,b=ie,N=0,R=se;R;R=_n(R))N++;for(R=0,Q=b;Q;Q=_n(Q))R++;for(;0<N-R;)se=_n(se),N--;for(;0<R-N;)b=_n(b),R--;for(;N--;){if(se===b||b!==null&&se===b.alternate)break t;se=_n(se),b=_n(b)}se=null}else se=null;ee!==null&&hu(H,V,ee,se,!1),ie!==null&&Ze!==null&&hu(H,Ze,ie,se,!0)}}e:{if(V=T?Mn(T):window,ee=V.nodeName&&V.nodeName.toLowerCase(),ee==="select"||ee==="input"&&V.type==="file")var ce=Zf;else if(Qs(V))if(Ys)ce=n0;else{ce=t0;var he=e0}else(ee=V.nodeName)&&ee.toLowerCase()==="input"&&(V.type==="checkbox"||V.type==="radio")&&(ce=r0);if(ce&&(ce=ce(e,T))){Ks(H,ce,r,W);break e}he&&he(e,V,T),e==="focusout"&&(he=V._wrapperState)&&he.controlled&&V.type==="number"&&jn(V,"number",V.value)}switch(he=T?Mn(T):window,e){case"focusin":(Qs(he)||he.contentEditable==="true")&&(Ln=he,Vi=T,jl=null);break;case"focusout":jl=Vi=Ln=null;break;case"mousedown":Wi=!0;break;case"contextmenu":case"mouseup":case"dragend":Wi=!1,ru(H,r,W);break;case"selectionchange":if(i0)break;case"keydown":case"keyup":ru(H,r,W)}var me;if(Ii)e:{switch(e){case"compositionstart":var ye="onCompositionStart";break e;case"compositionend":ye="onCompositionEnd";break e;case"compositionupdate":ye="onCompositionUpdate";break e}ye=void 0}else bn?Hs(e,r)&&(ye="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(ye="onCompositionStart");ye&&(Bs&&r.locale!=="ko"&&(bn||ye!=="onCompositionStart"?ye==="onCompositionEnd"&&bn&&(me=Os()):(Rr=W,Ti="value"in Rr?Rr.value:Rr.textContent,bn=!0)),he=jo(T,ye),0<he.length&&(ye=new Us(ye,e,null,r,W),H.push({event:ye,listeners:he}),me?ye.data=me:(me=$s(r),me!==null&&(ye.data=me)))),(me=Yf?Gf(e,r):Jf(e,r))&&(T=jo(T,"onBeforeInput"),0<T.length&&(W=new Us("onBeforeInput","beforeinput",null,r,W),H.push({event:W,listeners:T}),W.data=me))}du(H,t)})}function Nl(e,t,r){return{instance:e,listener:t,currentTarget:r}}function jo(e,t){for(var r=t+"Capture",n=[];e!==null;){var o=e,u=o.stateNode;o.tag===5&&u!==null&&(o=u,u=Zr(e,r),u!=null&&n.unshift(Nl(e,u,o)),u=Zr(e,t),u!=null&&n.push(Nl(e,u,o))),e=e.return}return n}function _n(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function hu(e,t,r,n,o){for(var u=t._reactName,f=[];r!==null&&r!==n;){var g=r,w=g.alternate,T=g.stateNode;if(w!==null&&w===n)break;g.tag===5&&T!==null&&(g=T,o?(w=Zr(r,u),w!=null&&f.unshift(Nl(r,w,g))):o||(w=Zr(r,u),w!=null&&f.push(Nl(r,w,g)))),r=r.return}f.length!==0&&e.push({event:t,listeners:f})}var c0=/\r\n?/g,d0=/\u0000|\uFFFD/g;function mu(e){return(typeof e=="string"?e:""+e).replace(c0,`
`).replace(d0,"")}function So(e,t,r){if(t=mu(t),mu(e)!==t&&r)throw Error(a(425))}function ko(){}var Gi=null,Ji=null;function Xi(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var qi=typeof setTimeout=="function"?setTimeout:void 0,f0=typeof clearTimeout=="function"?clearTimeout:void 0,pu=typeof Promise=="function"?Promise:void 0,h0=typeof queueMicrotask=="function"?queueMicrotask:typeof pu<"u"?function(e){return pu.resolve(null).then(e).catch(m0)}:qi;function m0(e){setTimeout(function(){throw e})}function Zi(e,t){var r=t,n=0;do{var o=r.nextSibling;if(e.removeChild(r),o&&o.nodeType===8)if(r=o.data,r==="/$"){if(n===0){e.removeChild(o),ml(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=o}while(r);ml(t)}function Tr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function gu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Tn=Math.random().toString(36).slice(2),tr="__reactFiber$"+Tn,El="__reactProps$"+Tn,ur="__reactContainer$"+Tn,ea="__reactEvents$"+Tn,p0="__reactListeners$"+Tn,g0="__reactHandles$"+Tn;function rn(e){var t=e[tr];if(t)return t;for(var r=e.parentNode;r;){if(t=r[ur]||r[tr]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=gu(e);e!==null;){if(r=e[tr])return r;e=gu(e)}return t}e=r,r=e.parentNode}return null}function Pl(e){return e=e[tr]||e[ur],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(a(33))}function No(e){return e[El]||null}var ta=[],zn=-1;function Mr(e){return{current:e}}function Qe(e){0>zn||(e.current=ta[zn],ta[zn]=null,zn--)}function Ve(e,t){zn++,ta[zn]=e.current,e.current=t}var zr={},ft=Mr(zr),xt=Mr(!1),nn=zr;function Dn(e,t){var r=e.type.contextTypes;if(!r)return zr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var o={},u;for(u in r)o[u]=t[u];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function wt(e){return e=e.childContextTypes,e!=null}function Eo(){Qe(xt),Qe(ft)}function vu(e,t,r){if(ft.current!==zr)throw Error(a(168));Ve(ft,t),Ve(xt,r)}function yu(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var o in n)if(!(o in t))throw Error(a(108,Le(e)||"Unknown",o));return I({},r,n)}function Po(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||zr,nn=ft.current,Ve(ft,e),Ve(xt,xt.current),!0}function xu(e,t,r){var n=e.stateNode;if(!n)throw Error(a(169));r?(e=yu(e,t,nn),n.__reactInternalMemoizedMergedChildContext=e,Qe(xt),Qe(ft),Ve(ft,e)):Qe(xt),Ve(xt,r)}var cr=null,Co=!1,ra=!1;function wu(e){cr===null?cr=[e]:cr.push(e)}function v0(e){Co=!0,wu(e)}function Dr(){if(!ra&&cr!==null){ra=!0;var e=0,t=Ie;try{var r=cr;for(Ie=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}cr=null,Co=!1}catch(o){throw cr!==null&&(cr=cr.slice(e+1)),He(ar,Dr),o}finally{Ie=t,ra=!1}}return null}var On=[],Fn=0,bo=null,Lo=0,Mt=[],zt=0,ln=null,dr=1,fr="";function on(e,t){On[Fn++]=Lo,On[Fn++]=bo,bo=e,Lo=t}function ju(e,t,r){Mt[zt++]=dr,Mt[zt++]=fr,Mt[zt++]=ln,ln=e;var n=dr;e=fr;var o=32-De(n)-1;n&=~(1<<o),r+=1;var u=32-De(t)+o;if(30<u){var f=o-o%5;u=(n&(1<<f)-1).toString(32),n>>=f,o-=f,dr=1<<32-De(t)+o|r<<o|n,fr=u+e}else dr=1<<u|r<<o|n,fr=e}function na(e){e.return!==null&&(on(e,1),ju(e,1,0))}function la(e){for(;e===bo;)bo=On[--Fn],On[Fn]=null,Lo=On[--Fn],On[Fn]=null;for(;e===ln;)ln=Mt[--zt],Mt[zt]=null,fr=Mt[--zt],Mt[zt]=null,dr=Mt[--zt],Mt[zt]=null}var bt=null,Lt=null,Ye=!1,Wt=null;function Su(e,t){var r=It(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function ku(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,bt=e,Lt=Tr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,bt=e,Lt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=ln!==null?{id:dr,overflow:fr}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=It(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,bt=e,Lt=null,!0):!1;default:return!1}}function oa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ia(e){if(Ye){var t=Lt;if(t){var r=t;if(!ku(e,t)){if(oa(e))throw Error(a(418));t=Tr(r.nextSibling);var n=bt;t&&ku(e,t)?Su(n,r):(e.flags=e.flags&-4097|2,Ye=!1,bt=e)}}else{if(oa(e))throw Error(a(418));e.flags=e.flags&-4097|2,Ye=!1,bt=e}}}function Nu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;bt=e}function Ro(e){if(e!==bt)return!1;if(!Ye)return Nu(e),Ye=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Xi(e.type,e.memoizedProps)),t&&(t=Lt)){if(oa(e))throw Eu(),Error(a(418));for(;t;)Su(e,t),t=Tr(t.nextSibling)}if(Nu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){Lt=Tr(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}Lt=null}}else Lt=bt?Tr(e.stateNode.nextSibling):null;return!0}function Eu(){for(var e=Lt;e;)e=Tr(e.nextSibling)}function In(){Lt=bt=null,Ye=!1}function aa(e){Wt===null?Wt=[e]:Wt.push(e)}var y0=te.ReactCurrentBatchConfig;function Cl(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(a(309));var n=r.stateNode}if(!n)throw Error(a(147,e));var o=n,u=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===u?t.ref:(t=function(f){var g=o.refs;f===null?delete g[u]:g[u]=f},t._stringRef=u,t)}if(typeof e!="string")throw Error(a(284));if(!r._owner)throw Error(a(290,e))}return e}function _o(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Pu(e){var t=e._init;return t(e._payload)}function Cu(e){function t(b,N){if(e){var R=b.deletions;R===null?(b.deletions=[N],b.flags|=16):R.push(N)}}function r(b,N){if(!e)return null;for(;N!==null;)t(b,N),N=N.sibling;return null}function n(b,N){for(b=new Map;N!==null;)N.key!==null?b.set(N.key,N):b.set(N.index,N),N=N.sibling;return b}function o(b,N){return b=Wr(b,N),b.index=0,b.sibling=null,b}function u(b,N,R){return b.index=R,e?(R=b.alternate,R!==null?(R=R.index,R<N?(b.flags|=2,N):R):(b.flags|=2,N)):(b.flags|=1048576,N)}function f(b){return e&&b.alternate===null&&(b.flags|=2),b}function g(b,N,R,Q){return N===null||N.tag!==6?(N=qa(R,b.mode,Q),N.return=b,N):(N=o(N,R),N.return=b,N)}function w(b,N,R,Q){var ce=R.type;return ce===L?W(b,N,R.props.children,Q,R.key):N!==null&&(N.elementType===ce||typeof ce=="object"&&ce!==null&&ce.$$typeof===Ke&&Pu(ce)===N.type)?(Q=o(N,R.props),Q.ref=Cl(b,N,R),Q.return=b,Q):(Q=ti(R.type,R.key,R.props,null,b.mode,Q),Q.ref=Cl(b,N,R),Q.return=b,Q)}function T(b,N,R,Q){return N===null||N.tag!==4||N.stateNode.containerInfo!==R.containerInfo||N.stateNode.implementation!==R.implementation?(N=Za(R,b.mode,Q),N.return=b,N):(N=o(N,R.children||[]),N.return=b,N)}function W(b,N,R,Q,ce){return N===null||N.tag!==7?(N=mn(R,b.mode,Q,ce),N.return=b,N):(N=o(N,R),N.return=b,N)}function H(b,N,R){if(typeof N=="string"&&N!==""||typeof N=="number")return N=qa(""+N,b.mode,R),N.return=b,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case X:return R=ti(N.type,N.key,N.props,null,b.mode,R),R.ref=Cl(b,null,N),R.return=b,R;case ge:return N=Za(N,b.mode,R),N.return=b,N;case Ke:var Q=N._init;return H(b,Q(N._payload),R)}if(Jr(N)||J(N))return N=mn(N,b.mode,R,null),N.return=b,N;_o(b,N)}return null}function V(b,N,R,Q){var ce=N!==null?N.key:null;if(typeof R=="string"&&R!==""||typeof R=="number")return ce!==null?null:g(b,N,""+R,Q);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case X:return R.key===ce?w(b,N,R,Q):null;case ge:return R.key===ce?T(b,N,R,Q):null;case Ke:return ce=R._init,V(b,N,ce(R._payload),Q)}if(Jr(R)||J(R))return ce!==null?null:W(b,N,R,Q,null);_o(b,R)}return null}function ee(b,N,R,Q,ce){if(typeof Q=="string"&&Q!==""||typeof Q=="number")return b=b.get(R)||null,g(N,b,""+Q,ce);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case X:return b=b.get(Q.key===null?R:Q.key)||null,w(N,b,Q,ce);case ge:return b=b.get(Q.key===null?R:Q.key)||null,T(N,b,Q,ce);case Ke:var he=Q._init;return ee(b,N,R,he(Q._payload),ce)}if(Jr(Q)||J(Q))return b=b.get(R)||null,W(N,b,Q,ce,null);_o(N,Q)}return null}function ie(b,N,R,Q){for(var ce=null,he=null,me=N,ye=N=0,st=null;me!==null&&ye<R.length;ye++){me.index>ye?(st=me,me=null):st=me.sibling;var Oe=V(b,me,R[ye],Q);if(Oe===null){me===null&&(me=st);break}e&&me&&Oe.alternate===null&&t(b,me),N=u(Oe,N,ye),he===null?ce=Oe:he.sibling=Oe,he=Oe,me=st}if(ye===R.length)return r(b,me),Ye&&on(b,ye),ce;if(me===null){for(;ye<R.length;ye++)me=H(b,R[ye],Q),me!==null&&(N=u(me,N,ye),he===null?ce=me:he.sibling=me,he=me);return Ye&&on(b,ye),ce}for(me=n(b,me);ye<R.length;ye++)st=ee(me,b,ye,R[ye],Q),st!==null&&(e&&st.alternate!==null&&me.delete(st.key===null?ye:st.key),N=u(st,N,ye),he===null?ce=st:he.sibling=st,he=st);return e&&me.forEach(function(Hr){return t(b,Hr)}),Ye&&on(b,ye),ce}function se(b,N,R,Q){var ce=J(R);if(typeof ce!="function")throw Error(a(150));if(R=ce.call(R),R==null)throw Error(a(151));for(var he=ce=null,me=N,ye=N=0,st=null,Oe=R.next();me!==null&&!Oe.done;ye++,Oe=R.next()){me.index>ye?(st=me,me=null):st=me.sibling;var Hr=V(b,me,Oe.value,Q);if(Hr===null){me===null&&(me=st);break}e&&me&&Hr.alternate===null&&t(b,me),N=u(Hr,N,ye),he===null?ce=Hr:he.sibling=Hr,he=Hr,me=st}if(Oe.done)return r(b,me),Ye&&on(b,ye),ce;if(me===null){for(;!Oe.done;ye++,Oe=R.next())Oe=H(b,Oe.value,Q),Oe!==null&&(N=u(Oe,N,ye),he===null?ce=Oe:he.sibling=Oe,he=Oe);return Ye&&on(b,ye),ce}for(me=n(b,me);!Oe.done;ye++,Oe=R.next())Oe=ee(me,b,ye,Oe.value,Q),Oe!==null&&(e&&Oe.alternate!==null&&me.delete(Oe.key===null?ye:Oe.key),N=u(Oe,N,ye),he===null?ce=Oe:he.sibling=Oe,he=Oe);return e&&me.forEach(function(X0){return t(b,X0)}),Ye&&on(b,ye),ce}function Ze(b,N,R,Q){if(typeof R=="object"&&R!==null&&R.type===L&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case X:e:{for(var ce=R.key,he=N;he!==null;){if(he.key===ce){if(ce=R.type,ce===L){if(he.tag===7){r(b,he.sibling),N=o(he,R.props.children),N.return=b,b=N;break e}}else if(he.elementType===ce||typeof ce=="object"&&ce!==null&&ce.$$typeof===Ke&&Pu(ce)===he.type){r(b,he.sibling),N=o(he,R.props),N.ref=Cl(b,he,R),N.return=b,b=N;break e}r(b,he);break}else t(b,he);he=he.sibling}R.type===L?(N=mn(R.props.children,b.mode,Q,R.key),N.return=b,b=N):(Q=ti(R.type,R.key,R.props,null,b.mode,Q),Q.ref=Cl(b,N,R),Q.return=b,b=Q)}return f(b);case ge:e:{for(he=R.key;N!==null;){if(N.key===he)if(N.tag===4&&N.stateNode.containerInfo===R.containerInfo&&N.stateNode.implementation===R.implementation){r(b,N.sibling),N=o(N,R.children||[]),N.return=b,b=N;break e}else{r(b,N);break}else t(b,N);N=N.sibling}N=Za(R,b.mode,Q),N.return=b,b=N}return f(b);case Ke:return he=R._init,Ze(b,N,he(R._payload),Q)}if(Jr(R))return ie(b,N,R,Q);if(J(R))return se(b,N,R,Q);_o(b,R)}return typeof R=="string"&&R!==""||typeof R=="number"?(R=""+R,N!==null&&N.tag===6?(r(b,N.sibling),N=o(N,R),N.return=b,b=N):(r(b,N),N=qa(R,b.mode,Q),N.return=b,b=N),f(b)):r(b,N)}return Ze}var Un=Cu(!0),bu=Cu(!1),To=Mr(null),Mo=null,An=null,sa=null;function ua(){sa=An=Mo=null}function ca(e){var t=To.current;Qe(To),e._currentValue=t}function da(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Bn(e,t){Mo=e,sa=An=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(jt=!0),e.firstContext=null)}function Dt(e){var t=e._currentValue;if(sa!==e)if(e={context:e,memoizedValue:t,next:null},An===null){if(Mo===null)throw Error(a(308));An=e,Mo.dependencies={lanes:0,firstContext:e}}else An=An.next=e;return t}var an=null;function fa(e){an===null?an=[e]:an.push(e)}function Lu(e,t,r,n){var o=t.interleaved;return o===null?(r.next=r,fa(t)):(r.next=o.next,o.next=r),t.interleaved=r,hr(e,n)}function hr(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Or=!1;function ha(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ru(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function mr(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Fr(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(Me&2)!==0){var o=n.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),n.pending=t,hr(e,r)}return o=n.interleaved,o===null?(t.next=t,fa(n)):(t.next=o.next,o.next=t),n.interleaved=t,hr(e,r)}function zo(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Ci(e,r)}}function _u(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var o=null,u=null;if(r=r.firstBaseUpdate,r!==null){do{var f={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};u===null?o=u=f:u=u.next=f,r=r.next}while(r!==null);u===null?o=u=t:u=u.next=t}else o=u=t;r={baseState:n.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Do(e,t,r,n){var o=e.updateQueue;Or=!1;var u=o.firstBaseUpdate,f=o.lastBaseUpdate,g=o.shared.pending;if(g!==null){o.shared.pending=null;var w=g,T=w.next;w.next=null,f===null?u=T:f.next=T,f=w;var W=e.alternate;W!==null&&(W=W.updateQueue,g=W.lastBaseUpdate,g!==f&&(g===null?W.firstBaseUpdate=T:g.next=T,W.lastBaseUpdate=w))}if(u!==null){var H=o.baseState;f=0,W=T=w=null,g=u;do{var V=g.lane,ee=g.eventTime;if((n&V)===V){W!==null&&(W=W.next={eventTime:ee,lane:0,tag:g.tag,payload:g.payload,callback:g.callback,next:null});e:{var ie=e,se=g;switch(V=t,ee=r,se.tag){case 1:if(ie=se.payload,typeof ie=="function"){H=ie.call(ee,H,V);break e}H=ie;break e;case 3:ie.flags=ie.flags&-65537|128;case 0:if(ie=se.payload,V=typeof ie=="function"?ie.call(ee,H,V):ie,V==null)break e;H=I({},H,V);break e;case 2:Or=!0}}g.callback!==null&&g.lane!==0&&(e.flags|=64,V=o.effects,V===null?o.effects=[g]:V.push(g))}else ee={eventTime:ee,lane:V,tag:g.tag,payload:g.payload,callback:g.callback,next:null},W===null?(T=W=ee,w=H):W=W.next=ee,f|=V;if(g=g.next,g===null){if(g=o.shared.pending,g===null)break;V=g,g=V.next,V.next=null,o.lastBaseUpdate=V,o.shared.pending=null}}while(!0);if(W===null&&(w=H),o.baseState=w,o.firstBaseUpdate=T,o.lastBaseUpdate=W,t=o.shared.interleaved,t!==null){o=t;do f|=o.lane,o=o.next;while(o!==t)}else u===null&&(o.shared.lanes=0);cn|=f,e.lanes=f,e.memoizedState=H}}function Tu(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],o=n.callback;if(o!==null){if(n.callback=null,n=r,typeof o!="function")throw Error(a(191,o));o.call(n)}}}var bl={},rr=Mr(bl),Ll=Mr(bl),Rl=Mr(bl);function sn(e){if(e===bl)throw Error(a(174));return e}function ma(e,t){switch(Ve(Rl,t),Ve(Ll,e),Ve(rr,bl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xr(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xr(t,e)}Qe(rr),Ve(rr,t)}function Vn(){Qe(rr),Qe(Ll),Qe(Rl)}function Mu(e){sn(Rl.current);var t=sn(rr.current),r=Xr(t,e.type);t!==r&&(Ve(Ll,e),Ve(rr,r))}function pa(e){Ll.current===e&&(Qe(rr),Qe(Ll))}var Je=Mr(0);function Oo(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ga=[];function va(){for(var e=0;e<ga.length;e++)ga[e]._workInProgressVersionPrimary=null;ga.length=0}var Fo=te.ReactCurrentDispatcher,ya=te.ReactCurrentBatchConfig,un=0,Xe=null,nt=null,it=null,Io=!1,_l=!1,Tl=0,x0=0;function ht(){throw Error(a(321))}function xa(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Vt(e[r],t[r]))return!1;return!0}function wa(e,t,r,n,o,u){if(un=u,Xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Fo.current=e===null||e.memoizedState===null?k0:N0,e=r(n,o),_l){u=0;do{if(_l=!1,Tl=0,25<=u)throw Error(a(301));u+=1,it=nt=null,t.updateQueue=null,Fo.current=E0,e=r(n,o)}while(_l)}if(Fo.current=Bo,t=nt!==null&&nt.next!==null,un=0,it=nt=Xe=null,Io=!1,t)throw Error(a(300));return e}function ja(){var e=Tl!==0;return Tl=0,e}function nr(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return it===null?Xe.memoizedState=it=e:it=it.next=e,it}function Ot(){if(nt===null){var e=Xe.alternate;e=e!==null?e.memoizedState:null}else e=nt.next;var t=it===null?Xe.memoizedState:it.next;if(t!==null)it=t,nt=e;else{if(e===null)throw Error(a(310));nt=e,e={memoizedState:nt.memoizedState,baseState:nt.baseState,baseQueue:nt.baseQueue,queue:nt.queue,next:null},it===null?Xe.memoizedState=it=e:it=it.next=e}return it}function Ml(e,t){return typeof t=="function"?t(e):t}function Sa(e){var t=Ot(),r=t.queue;if(r===null)throw Error(a(311));r.lastRenderedReducer=e;var n=nt,o=n.baseQueue,u=r.pending;if(u!==null){if(o!==null){var f=o.next;o.next=u.next,u.next=f}n.baseQueue=o=u,r.pending=null}if(o!==null){u=o.next,n=n.baseState;var g=f=null,w=null,T=u;do{var W=T.lane;if((un&W)===W)w!==null&&(w=w.next={lane:0,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null}),n=T.hasEagerState?T.eagerState:e(n,T.action);else{var H={lane:W,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null};w===null?(g=w=H,f=n):w=w.next=H,Xe.lanes|=W,cn|=W}T=T.next}while(T!==null&&T!==u);w===null?f=n:w.next=g,Vt(n,t.memoizedState)||(jt=!0),t.memoizedState=n,t.baseState=f,t.baseQueue=w,r.lastRenderedState=n}if(e=r.interleaved,e!==null){o=e;do u=o.lane,Xe.lanes|=u,cn|=u,o=o.next;while(o!==e)}else o===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function ka(e){var t=Ot(),r=t.queue;if(r===null)throw Error(a(311));r.lastRenderedReducer=e;var n=r.dispatch,o=r.pending,u=t.memoizedState;if(o!==null){r.pending=null;var f=o=o.next;do u=e(u,f.action),f=f.next;while(f!==o);Vt(u,t.memoizedState)||(jt=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),r.lastRenderedState=u}return[u,n]}function zu(){}function Du(e,t){var r=Xe,n=Ot(),o=t(),u=!Vt(n.memoizedState,o);if(u&&(n.memoizedState=o,jt=!0),n=n.queue,Na(Iu.bind(null,r,n,e),[e]),n.getSnapshot!==t||u||it!==null&&it.memoizedState.tag&1){if(r.flags|=2048,zl(9,Fu.bind(null,r,n,o,t),void 0,null),at===null)throw Error(a(349));(un&30)!==0||Ou(r,t,o)}return o}function Ou(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=Xe.updateQueue,t===null?(t={lastEffect:null,stores:null},Xe.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Fu(e,t,r,n){t.value=r,t.getSnapshot=n,Uu(t)&&Au(e)}function Iu(e,t,r){return r(function(){Uu(t)&&Au(e)})}function Uu(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Vt(e,r)}catch{return!0}}function Au(e){var t=hr(e,1);t!==null&&Kt(t,e,1,-1)}function Bu(e){var t=nr();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ml,lastRenderedState:e},t.queue=e,e=e.dispatch=S0.bind(null,Xe,e),[t.memoizedState,e]}function zl(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=Xe.updateQueue,t===null?(t={lastEffect:null,stores:null},Xe.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Vu(){return Ot().memoizedState}function Uo(e,t,r,n){var o=nr();Xe.flags|=e,o.memoizedState=zl(1|t,r,void 0,n===void 0?null:n)}function Ao(e,t,r,n){var o=Ot();n=n===void 0?null:n;var u=void 0;if(nt!==null){var f=nt.memoizedState;if(u=f.destroy,n!==null&&xa(n,f.deps)){o.memoizedState=zl(t,r,u,n);return}}Xe.flags|=e,o.memoizedState=zl(1|t,r,u,n)}function Wu(e,t){return Uo(8390656,8,e,t)}function Na(e,t){return Ao(2048,8,e,t)}function Hu(e,t){return Ao(4,2,e,t)}function $u(e,t){return Ao(4,4,e,t)}function Qu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ku(e,t,r){return r=r!=null?r.concat([e]):null,Ao(4,4,Qu.bind(null,t,e),r)}function Ea(){}function Yu(e,t){var r=Ot();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&xa(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Gu(e,t){var r=Ot();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&xa(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Ju(e,t,r){return(un&21)===0?(e.baseState&&(e.baseState=!1,jt=!0),e.memoizedState=r):(Vt(r,t)||(r=Ps(),Xe.lanes|=r,cn|=r,e.baseState=!0),t)}function w0(e,t){var r=Ie;Ie=r!==0&&4>r?r:4,e(!0);var n=ya.transition;ya.transition={};try{e(!1),t()}finally{Ie=r,ya.transition=n}}function Xu(){return Ot().memoizedState}function j0(e,t,r){var n=Br(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},qu(e))Zu(t,r);else if(r=Lu(e,t,r,n),r!==null){var o=yt();Kt(r,e,n,o),ec(r,t,n)}}function S0(e,t,r){var n=Br(e),o={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(qu(e))Zu(t,o);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var f=t.lastRenderedState,g=u(f,r);if(o.hasEagerState=!0,o.eagerState=g,Vt(g,f)){var w=t.interleaved;w===null?(o.next=o,fa(t)):(o.next=w.next,w.next=o),t.interleaved=o;return}}catch{}finally{}r=Lu(e,t,o,n),r!==null&&(o=yt(),Kt(r,e,n,o),ec(r,t,n))}}function qu(e){var t=e.alternate;return e===Xe||t!==null&&t===Xe}function Zu(e,t){_l=Io=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function ec(e,t,r){if((r&4194240)!==0){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Ci(e,r)}}var Bo={readContext:Dt,useCallback:ht,useContext:ht,useEffect:ht,useImperativeHandle:ht,useInsertionEffect:ht,useLayoutEffect:ht,useMemo:ht,useReducer:ht,useRef:ht,useState:ht,useDebugValue:ht,useDeferredValue:ht,useTransition:ht,useMutableSource:ht,useSyncExternalStore:ht,useId:ht,unstable_isNewReconciler:!1},k0={readContext:Dt,useCallback:function(e,t){return nr().memoizedState=[e,t===void 0?null:t],e},useContext:Dt,useEffect:Wu,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Uo(4194308,4,Qu.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Uo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Uo(4,2,e,t)},useMemo:function(e,t){var r=nr();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=nr();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=j0.bind(null,Xe,e),[n.memoizedState,e]},useRef:function(e){var t=nr();return e={current:e},t.memoizedState=e},useState:Bu,useDebugValue:Ea,useDeferredValue:function(e){return nr().memoizedState=e},useTransition:function(){var e=Bu(!1),t=e[0];return e=w0.bind(null,e[1]),nr().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=Xe,o=nr();if(Ye){if(r===void 0)throw Error(a(407));r=r()}else{if(r=t(),at===null)throw Error(a(349));(un&30)!==0||Ou(n,t,r)}o.memoizedState=r;var u={value:r,getSnapshot:t};return o.queue=u,Wu(Iu.bind(null,n,u,e),[e]),n.flags|=2048,zl(9,Fu.bind(null,n,u,r,t),void 0,null),r},useId:function(){var e=nr(),t=at.identifierPrefix;if(Ye){var r=fr,n=dr;r=(n&~(1<<32-De(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=Tl++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=x0++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},N0={readContext:Dt,useCallback:Yu,useContext:Dt,useEffect:Na,useImperativeHandle:Ku,useInsertionEffect:Hu,useLayoutEffect:$u,useMemo:Gu,useReducer:Sa,useRef:Vu,useState:function(){return Sa(Ml)},useDebugValue:Ea,useDeferredValue:function(e){var t=Ot();return Ju(t,nt.memoizedState,e)},useTransition:function(){var e=Sa(Ml)[0],t=Ot().memoizedState;return[e,t]},useMutableSource:zu,useSyncExternalStore:Du,useId:Xu,unstable_isNewReconciler:!1},E0={readContext:Dt,useCallback:Yu,useContext:Dt,useEffect:Na,useImperativeHandle:Ku,useInsertionEffect:Hu,useLayoutEffect:$u,useMemo:Gu,useReducer:ka,useRef:Vu,useState:function(){return ka(Ml)},useDebugValue:Ea,useDeferredValue:function(e){var t=Ot();return nt===null?t.memoizedState=e:Ju(t,nt.memoizedState,e)},useTransition:function(){var e=ka(Ml)[0],t=Ot().memoizedState;return[e,t]},useMutableSource:zu,useSyncExternalStore:Du,useId:Xu,unstable_isNewReconciler:!1};function Ht(e,t){if(e&&e.defaultProps){t=I({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Pa(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:I({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var Vo={isMounted:function(e){return(e=e._reactInternals)?le(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=yt(),o=Br(e),u=mr(n,o);u.payload=t,r!=null&&(u.callback=r),t=Fr(e,u,o),t!==null&&(Kt(t,e,o,n),zo(t,e,o))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=yt(),o=Br(e),u=mr(n,o);u.tag=1,u.payload=t,r!=null&&(u.callback=r),t=Fr(e,u,o),t!==null&&(Kt(t,e,o,n),zo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=yt(),n=Br(e),o=mr(r,n);o.tag=2,t!=null&&(o.callback=t),t=Fr(e,o,n),t!==null&&(Kt(t,e,n,r),zo(t,e,n))}};function tc(e,t,r,n,o,u,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,u,f):t.prototype&&t.prototype.isPureReactComponent?!wl(r,n)||!wl(o,u):!0}function rc(e,t,r){var n=!1,o=zr,u=t.contextType;return typeof u=="object"&&u!==null?u=Dt(u):(o=wt(t)?nn:ft.current,n=t.contextTypes,u=(n=n!=null)?Dn(e,o):zr),t=new t(r,u),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Vo,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=u),t}function nc(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&Vo.enqueueReplaceState(t,t.state,null)}function Ca(e,t,r,n){var o=e.stateNode;o.props=r,o.state=e.memoizedState,o.refs={},ha(e);var u=t.contextType;typeof u=="object"&&u!==null?o.context=Dt(u):(u=wt(t)?nn:ft.current,o.context=Dn(e,u)),o.state=e.memoizedState,u=t.getDerivedStateFromProps,typeof u=="function"&&(Pa(e,t,u,r),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Vo.enqueueReplaceState(o,o.state,null),Do(e,r,o,n),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Wn(e,t){try{var r="",n=t;do r+=ve(n),n=n.return;while(n);var o=r}catch(u){o=`
Error generating stack: `+u.message+`
`+u.stack}return{value:e,source:t,stack:o,digest:null}}function ba(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function La(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var P0=typeof WeakMap=="function"?WeakMap:Map;function lc(e,t,r){r=mr(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Go||(Go=!0,Ha=n),La(e,t)},r}function oc(e,t,r){r=mr(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var o=t.value;r.payload=function(){return n(o)},r.callback=function(){La(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(r.callback=function(){La(e,t),typeof n!="function"&&(Ur===null?Ur=new Set([this]):Ur.add(this));var f=t.stack;this.componentDidCatch(t.value,{componentStack:f!==null?f:""})}),r}function ic(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new P0;var o=new Set;n.set(t,o)}else o=n.get(t),o===void 0&&(o=new Set,n.set(t,o));o.has(r)||(o.add(r),e=A0.bind(null,e,t,r),t.then(e,e))}function ac(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function sc(e,t,r,n,o){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=mr(-1,1),t.tag=2,Fr(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var C0=te.ReactCurrentOwner,jt=!1;function vt(e,t,r,n){t.child=e===null?bu(t,null,r,n):Un(t,e.child,r,n)}function uc(e,t,r,n,o){r=r.render;var u=t.ref;return Bn(t,o),n=wa(e,t,r,n,u,o),r=ja(),e!==null&&!jt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,pr(e,t,o)):(Ye&&r&&na(t),t.flags|=1,vt(e,t,n,o),t.child)}function cc(e,t,r,n,o){if(e===null){var u=r.type;return typeof u=="function"&&!Xa(u)&&u.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=u,dc(e,t,u,n,o)):(e=ti(r.type,null,n,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,(e.lanes&o)===0){var f=u.memoizedProps;if(r=r.compare,r=r!==null?r:wl,r(f,n)&&e.ref===t.ref)return pr(e,t,o)}return t.flags|=1,e=Wr(u,n),e.ref=t.ref,e.return=t,t.child=e}function dc(e,t,r,n,o){if(e!==null){var u=e.memoizedProps;if(wl(u,n)&&e.ref===t.ref)if(jt=!1,t.pendingProps=n=u,(e.lanes&o)!==0)(e.flags&131072)!==0&&(jt=!0);else return t.lanes=e.lanes,pr(e,t,o)}return Ra(e,t,r,n,o)}function fc(e,t,r){var n=t.pendingProps,o=n.children,u=e!==null?e.memoizedState:null;if(n.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ve($n,Rt),Rt|=r;else{if((r&1073741824)===0)return e=u!==null?u.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ve($n,Rt),Rt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=u!==null?u.baseLanes:r,Ve($n,Rt),Rt|=n}else u!==null?(n=u.baseLanes|r,t.memoizedState=null):n=r,Ve($n,Rt),Rt|=n;return vt(e,t,o,r),t.child}function hc(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Ra(e,t,r,n,o){var u=wt(r)?nn:ft.current;return u=Dn(t,u),Bn(t,o),r=wa(e,t,r,n,u,o),n=ja(),e!==null&&!jt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,pr(e,t,o)):(Ye&&n&&na(t),t.flags|=1,vt(e,t,r,o),t.child)}function mc(e,t,r,n,o){if(wt(r)){var u=!0;Po(t)}else u=!1;if(Bn(t,o),t.stateNode===null)Ho(e,t),rc(t,r,n),Ca(t,r,n,o),n=!0;else if(e===null){var f=t.stateNode,g=t.memoizedProps;f.props=g;var w=f.context,T=r.contextType;typeof T=="object"&&T!==null?T=Dt(T):(T=wt(r)?nn:ft.current,T=Dn(t,T));var W=r.getDerivedStateFromProps,H=typeof W=="function"||typeof f.getSnapshotBeforeUpdate=="function";H||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==n||w!==T)&&nc(t,f,n,T),Or=!1;var V=t.memoizedState;f.state=V,Do(t,n,f,o),w=t.memoizedState,g!==n||V!==w||xt.current||Or?(typeof W=="function"&&(Pa(t,r,W,n),w=t.memoizedState),(g=Or||tc(t,r,g,n,V,w,T))?(H||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(t.flags|=4194308)):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=w),f.props=n,f.state=w,f.context=T,n=g):(typeof f.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{f=t.stateNode,Ru(e,t),g=t.memoizedProps,T=t.type===t.elementType?g:Ht(t.type,g),f.props=T,H=t.pendingProps,V=f.context,w=r.contextType,typeof w=="object"&&w!==null?w=Dt(w):(w=wt(r)?nn:ft.current,w=Dn(t,w));var ee=r.getDerivedStateFromProps;(W=typeof ee=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==H||V!==w)&&nc(t,f,n,w),Or=!1,V=t.memoizedState,f.state=V,Do(t,n,f,o);var ie=t.memoizedState;g!==H||V!==ie||xt.current||Or?(typeof ee=="function"&&(Pa(t,r,ee,n),ie=t.memoizedState),(T=Or||tc(t,r,T,n,V,ie,w)||!1)?(W||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(n,ie,w),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(n,ie,w)),typeof f.componentDidUpdate=="function"&&(t.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&V===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&V===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=ie),f.props=n,f.state=ie,f.context=w,n=T):(typeof f.componentDidUpdate!="function"||g===e.memoizedProps&&V===e.memoizedState||(t.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&V===e.memoizedState||(t.flags|=1024),n=!1)}return _a(e,t,r,n,u,o)}function _a(e,t,r,n,o,u){hc(e,t);var f=(t.flags&128)!==0;if(!n&&!f)return o&&xu(t,r,!1),pr(e,t,u);n=t.stateNode,C0.current=t;var g=f&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&f?(t.child=Un(t,e.child,null,u),t.child=Un(t,null,g,u)):vt(e,t,g,u),t.memoizedState=n.state,o&&xu(t,r,!0),t.child}function pc(e){var t=e.stateNode;t.pendingContext?vu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&vu(e,t.context,!1),ma(e,t.containerInfo)}function gc(e,t,r,n,o){return In(),aa(o),t.flags|=256,vt(e,t,r,n),t.child}var Ta={dehydrated:null,treeContext:null,retryLane:0};function Ma(e){return{baseLanes:e,cachePool:null,transitions:null}}function vc(e,t,r){var n=t.pendingProps,o=Je.current,u=!1,f=(t.flags&128)!==0,g;if((g=f)||(g=e!==null&&e.memoizedState===null?!1:(o&2)!==0),g?(u=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),Ve(Je,o&1),e===null)return ia(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(f=n.children,e=n.fallback,u?(n=t.mode,u=t.child,f={mode:"hidden",children:f},(n&1)===0&&u!==null?(u.childLanes=0,u.pendingProps=f):u=ri(f,n,0,null),e=mn(e,n,r,null),u.return=t,e.return=t,u.sibling=e,t.child=u,t.child.memoizedState=Ma(r),t.memoizedState=Ta,e):za(t,f));if(o=e.memoizedState,o!==null&&(g=o.dehydrated,g!==null))return b0(e,t,f,n,g,o,r);if(u){u=n.fallback,f=t.mode,o=e.child,g=o.sibling;var w={mode:"hidden",children:n.children};return(f&1)===0&&t.child!==o?(n=t.child,n.childLanes=0,n.pendingProps=w,t.deletions=null):(n=Wr(o,w),n.subtreeFlags=o.subtreeFlags&14680064),g!==null?u=Wr(g,u):(u=mn(u,f,r,null),u.flags|=2),u.return=t,n.return=t,n.sibling=u,t.child=n,n=u,u=t.child,f=e.child.memoizedState,f=f===null?Ma(r):{baseLanes:f.baseLanes|r,cachePool:null,transitions:f.transitions},u.memoizedState=f,u.childLanes=e.childLanes&~r,t.memoizedState=Ta,n}return u=e.child,e=u.sibling,n=Wr(u,{mode:"visible",children:n.children}),(t.mode&1)===0&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function za(e,t){return t=ri({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Wo(e,t,r,n){return n!==null&&aa(n),Un(t,e.child,null,r),e=za(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function b0(e,t,r,n,o,u,f){if(r)return t.flags&256?(t.flags&=-257,n=ba(Error(a(422))),Wo(e,t,f,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(u=n.fallback,o=t.mode,n=ri({mode:"visible",children:n.children},o,0,null),u=mn(u,o,f,null),u.flags|=2,n.return=t,u.return=t,n.sibling=u,t.child=n,(t.mode&1)!==0&&Un(t,e.child,null,f),t.child.memoizedState=Ma(f),t.memoizedState=Ta,u);if((t.mode&1)===0)return Wo(e,t,f,null);if(o.data==="$!"){if(n=o.nextSibling&&o.nextSibling.dataset,n)var g=n.dgst;return n=g,u=Error(a(419)),n=ba(u,n,void 0),Wo(e,t,f,n)}if(g=(f&e.childLanes)!==0,jt||g){if(n=at,n!==null){switch(f&-f){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=(o&(n.suspendedLanes|f))!==0?0:o,o!==0&&o!==u.retryLane&&(u.retryLane=o,hr(e,o),Kt(n,e,o,-1))}return Ja(),n=ba(Error(a(421))),Wo(e,t,f,n)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=B0.bind(null,e),o._reactRetry=t,null):(e=u.treeContext,Lt=Tr(o.nextSibling),bt=t,Ye=!0,Wt=null,e!==null&&(Mt[zt++]=dr,Mt[zt++]=fr,Mt[zt++]=ln,dr=e.id,fr=e.overflow,ln=t),t=za(t,n.children),t.flags|=4096,t)}function yc(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),da(e.return,t,r)}function Da(e,t,r,n,o){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:o}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=n,u.tail=r,u.tailMode=o)}function xc(e,t,r){var n=t.pendingProps,o=n.revealOrder,u=n.tail;if(vt(e,t,n.children,r),n=Je.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&yc(e,r,t);else if(e.tag===19)yc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(Ve(Je,n),(t.mode&1)===0)t.memoizedState=null;else switch(o){case"forwards":for(r=t.child,o=null;r!==null;)e=r.alternate,e!==null&&Oo(e)===null&&(o=r),r=r.sibling;r=o,r===null?(o=t.child,t.child=null):(o=r.sibling,r.sibling=null),Da(t,!1,o,r,u);break;case"backwards":for(r=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Oo(e)===null){t.child=o;break}e=o.sibling,o.sibling=r,r=o,o=e}Da(t,!0,r,null,u);break;case"together":Da(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ho(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function pr(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),cn|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(a(153));if(t.child!==null){for(e=t.child,r=Wr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Wr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function L0(e,t,r){switch(t.tag){case 3:pc(t),In();break;case 5:Mu(t);break;case 1:wt(t.type)&&Po(t);break;case 4:ma(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,o=t.memoizedProps.value;Ve(To,n._currentValue),n._currentValue=o;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(Ve(Je,Je.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?vc(e,t,r):(Ve(Je,Je.current&1),e=pr(e,t,r),e!==null?e.sibling:null);Ve(Je,Je.current&1);break;case 19:if(n=(r&t.childLanes)!==0,(e.flags&128)!==0){if(n)return xc(e,t,r);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),Ve(Je,Je.current),n)break;return null;case 22:case 23:return t.lanes=0,fc(e,t,r)}return pr(e,t,r)}var wc,Oa,jc,Sc;wc=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Oa=function(){},jc=function(e,t,r,n){var o=e.memoizedProps;if(o!==n){e=t.stateNode,sn(rr.current);var u=null;switch(r){case"input":o=wn(e,o),n=wn(e,n),u=[];break;case"select":o=I({},o,{value:void 0}),n=I({},n,{value:void 0}),u=[];break;case"textarea":o=tl(e,o),n=tl(e,n),u=[];break;default:typeof o.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=ko)}Sn(r,n);var f;r=null;for(T in o)if(!n.hasOwnProperty(T)&&o.hasOwnProperty(T)&&o[T]!=null)if(T==="style"){var g=o[T];for(f in g)g.hasOwnProperty(f)&&(r||(r={}),r[f]="")}else T!=="dangerouslySetInnerHTML"&&T!=="children"&&T!=="suppressContentEditableWarning"&&T!=="suppressHydrationWarning"&&T!=="autoFocus"&&(d.hasOwnProperty(T)?u||(u=[]):(u=u||[]).push(T,null));for(T in n){var w=n[T];if(g=o!=null?o[T]:void 0,n.hasOwnProperty(T)&&w!==g&&(w!=null||g!=null))if(T==="style")if(g){for(f in g)!g.hasOwnProperty(f)||w&&w.hasOwnProperty(f)||(r||(r={}),r[f]="");for(f in w)w.hasOwnProperty(f)&&g[f]!==w[f]&&(r||(r={}),r[f]=w[f])}else r||(u||(u=[]),u.push(T,r)),r=w;else T==="dangerouslySetInnerHTML"?(w=w?w.__html:void 0,g=g?g.__html:void 0,w!=null&&g!==w&&(u=u||[]).push(T,w)):T==="children"?typeof w!="string"&&typeof w!="number"||(u=u||[]).push(T,""+w):T!=="suppressContentEditableWarning"&&T!=="suppressHydrationWarning"&&(d.hasOwnProperty(T)?(w!=null&&T==="onScroll"&&$e("scroll",e),u||g===w||(u=[])):(u=u||[]).push(T,w))}r&&(u=u||[]).push("style",r);var T=u;(t.updateQueue=T)&&(t.flags|=4)}},Sc=function(e,t,r,n){r!==n&&(t.flags|=4)};function Dl(e,t){if(!Ye)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function mt(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var o=e.child;o!==null;)r|=o.lanes|o.childLanes,n|=o.subtreeFlags&14680064,n|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)r|=o.lanes|o.childLanes,n|=o.subtreeFlags,n|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function R0(e,t,r){var n=t.pendingProps;switch(la(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return mt(t),null;case 1:return wt(t.type)&&Eo(),mt(t),null;case 3:return n=t.stateNode,Vn(),Qe(xt),Qe(ft),va(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Ro(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Wt!==null&&(Ka(Wt),Wt=null))),Oa(e,t),mt(t),null;case 5:pa(t);var o=sn(Rl.current);if(r=t.type,e!==null&&t.stateNode!=null)jc(e,t,r,n,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(a(166));return mt(t),null}if(e=sn(rr.current),Ro(t)){n=t.stateNode,r=t.type;var u=t.memoizedProps;switch(n[tr]=t,n[El]=u,e=(t.mode&1)!==0,r){case"dialog":$e("cancel",n),$e("close",n);break;case"iframe":case"object":case"embed":$e("load",n);break;case"video":case"audio":for(o=0;o<Sl.length;o++)$e(Sl[o],n);break;case"source":$e("error",n);break;case"img":case"image":case"link":$e("error",n),$e("load",n);break;case"details":$e("toggle",n);break;case"input":Xl(n,u),$e("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!u.multiple},$e("invalid",n);break;case"textarea":Zl(n,u),$e("invalid",n)}Sn(r,u),o=null;for(var f in u)if(u.hasOwnProperty(f)){var g=u[f];f==="children"?typeof g=="string"?n.textContent!==g&&(u.suppressHydrationWarning!==!0&&So(n.textContent,g,e),o=["children",g]):typeof g=="number"&&n.textContent!==""+g&&(u.suppressHydrationWarning!==!0&&So(n.textContent,g,e),o=["children",""+g]):d.hasOwnProperty(f)&&g!=null&&f==="onScroll"&&$e("scroll",n)}switch(r){case"input":xn(n),ql(n,u,!0);break;case"textarea":xn(n),jr(n);break;case"select":case"option":break;default:typeof u.onClick=="function"&&(n.onclick=ko)}n=o,t.updateQueue=n,n!==null&&(t.flags|=4)}else{f=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=rl(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=f.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=f.createElement(r,{is:n.is}):(e=f.createElement(r),r==="select"&&(f=e,n.multiple?f.multiple=!0:n.size&&(f.size=n.size))):e=f.createElementNS(e,r),e[tr]=t,e[El]=n,wc(e,t,!1,!1),t.stateNode=e;e:{switch(f=ll(r,n),r){case"dialog":$e("cancel",e),$e("close",e),o=n;break;case"iframe":case"object":case"embed":$e("load",e),o=n;break;case"video":case"audio":for(o=0;o<Sl.length;o++)$e(Sl[o],e);o=n;break;case"source":$e("error",e),o=n;break;case"img":case"image":case"link":$e("error",e),$e("load",e),o=n;break;case"details":$e("toggle",e),o=n;break;case"input":Xl(e,n),o=wn(e,n),$e("invalid",e);break;case"option":o=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},o=I({},n,{value:void 0}),$e("invalid",e);break;case"textarea":Zl(e,n),o=tl(e,n),$e("invalid",e);break;default:o=n}Sn(r,o),g=o;for(u in g)if(g.hasOwnProperty(u)){var w=g[u];u==="style"?nl(e,w):u==="dangerouslySetInnerHTML"?(w=w?w.__html:void 0,w!=null&&Tt(e,w)):u==="children"?typeof w=="string"?(r!=="textarea"||w!=="")&&Sr(e,w):typeof w=="number"&&Sr(e,""+w):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(d.hasOwnProperty(u)?w!=null&&u==="onScroll"&&$e("scroll",e):w!=null&&K(e,u,w,f))}switch(r){case"input":xn(e),ql(e,n,!1);break;case"textarea":xn(e),jr(e);break;case"option":n.value!=null&&e.setAttribute("value",""+Re(n.value));break;case"select":e.multiple=!!n.multiple,u=n.value,u!=null?wr(e,!!n.multiple,u,!1):n.defaultValue!=null&&wr(e,!!n.multiple,n.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=ko)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return mt(t),null;case 6:if(e&&t.stateNode!=null)Sc(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(a(166));if(r=sn(Rl.current),sn(rr.current),Ro(t)){if(n=t.stateNode,r=t.memoizedProps,n[tr]=t,(u=n.nodeValue!==r)&&(e=bt,e!==null))switch(e.tag){case 3:So(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&So(n.nodeValue,r,(e.mode&1)!==0)}u&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[tr]=t,t.stateNode=n}return mt(t),null;case 13:if(Qe(Je),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ye&&Lt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Eu(),In(),t.flags|=98560,u=!1;else if(u=Ro(t),n!==null&&n.dehydrated!==null){if(e===null){if(!u)throw Error(a(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(a(317));u[tr]=t}else In(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;mt(t),u=!1}else Wt!==null&&(Ka(Wt),Wt=null),u=!0;if(!u)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Je.current&1)!==0?lt===0&&(lt=3):Ja())),t.updateQueue!==null&&(t.flags|=4),mt(t),null);case 4:return Vn(),Oa(e,t),e===null&&kl(t.stateNode.containerInfo),mt(t),null;case 10:return ca(t.type._context),mt(t),null;case 17:return wt(t.type)&&Eo(),mt(t),null;case 19:if(Qe(Je),u=t.memoizedState,u===null)return mt(t),null;if(n=(t.flags&128)!==0,f=u.rendering,f===null)if(n)Dl(u,!1);else{if(lt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(f=Oo(e),f!==null){for(t.flags|=128,Dl(u,!1),n=f.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)u=r,e=n,u.flags&=14680066,f=u.alternate,f===null?(u.childLanes=0,u.lanes=e,u.child=null,u.subtreeFlags=0,u.memoizedProps=null,u.memoizedState=null,u.updateQueue=null,u.dependencies=null,u.stateNode=null):(u.childLanes=f.childLanes,u.lanes=f.lanes,u.child=f.child,u.subtreeFlags=0,u.deletions=null,u.memoizedProps=f.memoizedProps,u.memoizedState=f.memoizedState,u.updateQueue=f.updateQueue,u.type=f.type,e=f.dependencies,u.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Ve(Je,Je.current&1|2),t.child}e=e.sibling}u.tail!==null&&ze()>Qn&&(t.flags|=128,n=!0,Dl(u,!1),t.lanes=4194304)}else{if(!n)if(e=Oo(f),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Dl(u,!0),u.tail===null&&u.tailMode==="hidden"&&!f.alternate&&!Ye)return mt(t),null}else 2*ze()-u.renderingStartTime>Qn&&r!==1073741824&&(t.flags|=128,n=!0,Dl(u,!1),t.lanes=4194304);u.isBackwards?(f.sibling=t.child,t.child=f):(r=u.last,r!==null?r.sibling=f:t.child=f,u.last=f)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=ze(),t.sibling=null,r=Je.current,Ve(Je,n?r&1|2:r&1),t):(mt(t),null);case 22:case 23:return Ga(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&(t.mode&1)!==0?(Rt&1073741824)!==0&&(mt(t),t.subtreeFlags&6&&(t.flags|=8192)):mt(t),null;case 24:return null;case 25:return null}throw Error(a(156,t.tag))}function _0(e,t){switch(la(t),t.tag){case 1:return wt(t.type)&&Eo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Vn(),Qe(xt),Qe(ft),va(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return pa(t),null;case 13:if(Qe(Je),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(a(340));In()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Qe(Je),null;case 4:return Vn(),null;case 10:return ca(t.type._context),null;case 22:case 23:return Ga(),null;case 24:return null;default:return null}}var $o=!1,pt=!1,T0=typeof WeakSet=="function"?WeakSet:Set,ne=null;function Hn(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){qe(e,t,n)}else r.current=null}function Fa(e,t,r){try{r()}catch(n){qe(e,t,n)}}var kc=!1;function M0(e,t){if(Gi=co,e=tu(),Bi(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var o=n.anchorOffset,u=n.focusNode;n=n.focusOffset;try{r.nodeType,u.nodeType}catch{r=null;break e}var f=0,g=-1,w=-1,T=0,W=0,H=e,V=null;t:for(;;){for(var ee;H!==r||o!==0&&H.nodeType!==3||(g=f+o),H!==u||n!==0&&H.nodeType!==3||(w=f+n),H.nodeType===3&&(f+=H.nodeValue.length),(ee=H.firstChild)!==null;)V=H,H=ee;for(;;){if(H===e)break t;if(V===r&&++T===o&&(g=f),V===u&&++W===n&&(w=f),(ee=H.nextSibling)!==null)break;H=V,V=H.parentNode}H=ee}r=g===-1||w===-1?null:{start:g,end:w}}else r=null}r=r||{start:0,end:0}}else r=null;for(Ji={focusedElem:e,selectionRange:r},co=!1,ne=t;ne!==null;)if(t=ne,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,ne=e;else for(;ne!==null;){t=ne;try{var ie=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ie!==null){var se=ie.memoizedProps,Ze=ie.memoizedState,b=t.stateNode,N=b.getSnapshotBeforeUpdate(t.elementType===t.type?se:Ht(t.type,se),Ze);b.__reactInternalSnapshotBeforeUpdate=N}break;case 3:var R=t.stateNode.containerInfo;R.nodeType===1?R.textContent="":R.nodeType===9&&R.documentElement&&R.removeChild(R.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(a(163))}}catch(Q){qe(t,t.return,Q)}if(e=t.sibling,e!==null){e.return=t.return,ne=e;break}ne=t.return}return ie=kc,kc=!1,ie}function Ol(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var o=n=n.next;do{if((o.tag&e)===e){var u=o.destroy;o.destroy=void 0,u!==void 0&&Fa(t,r,u)}o=o.next}while(o!==n)}}function Qo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function Ia(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Nc(e){var t=e.alternate;t!==null&&(e.alternate=null,Nc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tr],delete t[El],delete t[ea],delete t[p0],delete t[g0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ec(e){return e.tag===5||e.tag===3||e.tag===4}function Pc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ec(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ua(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=ko));else if(n!==4&&(e=e.child,e!==null))for(Ua(e,t,r),e=e.sibling;e!==null;)Ua(e,t,r),e=e.sibling}function Aa(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(Aa(e,t,r),e=e.sibling;e!==null;)Aa(e,t,r),e=e.sibling}var ct=null,$t=!1;function Ir(e,t,r){for(r=r.child;r!==null;)Cc(e,t,r),r=r.sibling}function Cc(e,t,r){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(Ae,r)}catch{}switch(r.tag){case 5:pt||Hn(r,t);case 6:var n=ct,o=$t;ct=null,Ir(e,t,r),ct=n,$t=o,ct!==null&&($t?(e=ct,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):ct.removeChild(r.stateNode));break;case 18:ct!==null&&($t?(e=ct,r=r.stateNode,e.nodeType===8?Zi(e.parentNode,r):e.nodeType===1&&Zi(e,r),ml(e)):Zi(ct,r.stateNode));break;case 4:n=ct,o=$t,ct=r.stateNode.containerInfo,$t=!0,Ir(e,t,r),ct=n,$t=o;break;case 0:case 11:case 14:case 15:if(!pt&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){o=n=n.next;do{var u=o,f=u.destroy;u=u.tag,f!==void 0&&((u&2)!==0||(u&4)!==0)&&Fa(r,t,f),o=o.next}while(o!==n)}Ir(e,t,r);break;case 1:if(!pt&&(Hn(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(g){qe(r,t,g)}Ir(e,t,r);break;case 21:Ir(e,t,r);break;case 22:r.mode&1?(pt=(n=pt)||r.memoizedState!==null,Ir(e,t,r),pt=n):Ir(e,t,r);break;default:Ir(e,t,r)}}function bc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new T0),t.forEach(function(n){var o=V0.bind(null,e,n);r.has(n)||(r.add(n),n.then(o,o))})}}function Qt(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var o=r[n];try{var u=e,f=t,g=f;e:for(;g!==null;){switch(g.tag){case 5:ct=g.stateNode,$t=!1;break e;case 3:ct=g.stateNode.containerInfo,$t=!0;break e;case 4:ct=g.stateNode.containerInfo,$t=!0;break e}g=g.return}if(ct===null)throw Error(a(160));Cc(u,f,o),ct=null,$t=!1;var w=o.alternate;w!==null&&(w.return=null),o.return=null}catch(T){qe(o,t,T)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Lc(t,e),t=t.sibling}function Lc(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Qt(t,e),lr(e),n&4){try{Ol(3,e,e.return),Qo(3,e)}catch(se){qe(e,e.return,se)}try{Ol(5,e,e.return)}catch(se){qe(e,e.return,se)}}break;case 1:Qt(t,e),lr(e),n&512&&r!==null&&Hn(r,r.return);break;case 5:if(Qt(t,e),lr(e),n&512&&r!==null&&Hn(r,r.return),e.flags&32){var o=e.stateNode;try{Sr(o,"")}catch(se){qe(e,e.return,se)}}if(n&4&&(o=e.stateNode,o!=null)){var u=e.memoizedProps,f=r!==null?r.memoizedProps:u,g=e.type,w=e.updateQueue;if(e.updateQueue=null,w!==null)try{g==="input"&&u.type==="radio"&&u.name!=null&&Gt(o,u),ll(g,f);var T=ll(g,u);for(f=0;f<w.length;f+=2){var W=w[f],H=w[f+1];W==="style"?nl(o,H):W==="dangerouslySetInnerHTML"?Tt(o,H):W==="children"?Sr(o,H):K(o,W,H,T)}switch(g){case"input":el(o,u);break;case"textarea":Jt(o,u);break;case"select":var V=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!u.multiple;var ee=u.value;ee!=null?wr(o,!!u.multiple,ee,!1):V!==!!u.multiple&&(u.defaultValue!=null?wr(o,!!u.multiple,u.defaultValue,!0):wr(o,!!u.multiple,u.multiple?[]:"",!1))}o[El]=u}catch(se){qe(e,e.return,se)}}break;case 6:if(Qt(t,e),lr(e),n&4){if(e.stateNode===null)throw Error(a(162));o=e.stateNode,u=e.memoizedProps;try{o.nodeValue=u}catch(se){qe(e,e.return,se)}}break;case 3:if(Qt(t,e),lr(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{ml(t.containerInfo)}catch(se){qe(e,e.return,se)}break;case 4:Qt(t,e),lr(e);break;case 13:Qt(t,e),lr(e),o=e.child,o.flags&8192&&(u=o.memoizedState!==null,o.stateNode.isHidden=u,!u||o.alternate!==null&&o.alternate.memoizedState!==null||(Wa=ze())),n&4&&bc(e);break;case 22:if(W=r!==null&&r.memoizedState!==null,e.mode&1?(pt=(T=pt)||W,Qt(t,e),pt=T):Qt(t,e),lr(e),n&8192){if(T=e.memoizedState!==null,(e.stateNode.isHidden=T)&&!W&&(e.mode&1)!==0)for(ne=e,W=e.child;W!==null;){for(H=ne=W;ne!==null;){switch(V=ne,ee=V.child,V.tag){case 0:case 11:case 14:case 15:Ol(4,V,V.return);break;case 1:Hn(V,V.return);var ie=V.stateNode;if(typeof ie.componentWillUnmount=="function"){n=V,r=V.return;try{t=n,ie.props=t.memoizedProps,ie.state=t.memoizedState,ie.componentWillUnmount()}catch(se){qe(n,r,se)}}break;case 5:Hn(V,V.return);break;case 22:if(V.memoizedState!==null){Tc(H);continue}}ee!==null?(ee.return=V,ne=ee):Tc(H)}W=W.sibling}e:for(W=null,H=e;;){if(H.tag===5){if(W===null){W=H;try{o=H.stateNode,T?(u=o.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none"):(g=H.stateNode,w=H.memoizedProps.style,f=w!=null&&w.hasOwnProperty("display")?w.display:null,g.style.display=At("display",f))}catch(se){qe(e,e.return,se)}}}else if(H.tag===6){if(W===null)try{H.stateNode.nodeValue=T?"":H.memoizedProps}catch(se){qe(e,e.return,se)}}else if((H.tag!==22&&H.tag!==23||H.memoizedState===null||H===e)&&H.child!==null){H.child.return=H,H=H.child;continue}if(H===e)break e;for(;H.sibling===null;){if(H.return===null||H.return===e)break e;W===H&&(W=null),H=H.return}W===H&&(W=null),H.sibling.return=H.return,H=H.sibling}}break;case 19:Qt(t,e),lr(e),n&4&&bc(e);break;case 21:break;default:Qt(t,e),lr(e)}}function lr(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Ec(r)){var n=r;break e}r=r.return}throw Error(a(160))}switch(n.tag){case 5:var o=n.stateNode;n.flags&32&&(Sr(o,""),n.flags&=-33);var u=Pc(e);Aa(e,u,o);break;case 3:case 4:var f=n.stateNode.containerInfo,g=Pc(e);Ua(e,g,f);break;default:throw Error(a(161))}}catch(w){qe(e,e.return,w)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function z0(e,t,r){ne=e,Rc(e)}function Rc(e,t,r){for(var n=(e.mode&1)!==0;ne!==null;){var o=ne,u=o.child;if(o.tag===22&&n){var f=o.memoizedState!==null||$o;if(!f){var g=o.alternate,w=g!==null&&g.memoizedState!==null||pt;g=$o;var T=pt;if($o=f,(pt=w)&&!T)for(ne=o;ne!==null;)f=ne,w=f.child,f.tag===22&&f.memoizedState!==null?Mc(o):w!==null?(w.return=f,ne=w):Mc(o);for(;u!==null;)ne=u,Rc(u),u=u.sibling;ne=o,$o=g,pt=T}_c(e)}else(o.subtreeFlags&8772)!==0&&u!==null?(u.return=o,ne=u):_c(e)}}function _c(e){for(;ne!==null;){var t=ne;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:pt||Qo(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!pt)if(r===null)n.componentDidMount();else{var o=t.elementType===t.type?r.memoizedProps:Ht(t.type,r.memoizedProps);n.componentDidUpdate(o,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var u=t.updateQueue;u!==null&&Tu(t,u,n);break;case 3:var f=t.updateQueue;if(f!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}Tu(t,f,r)}break;case 5:var g=t.stateNode;if(r===null&&t.flags&4){r=g;var w=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":w.autoFocus&&r.focus();break;case"img":w.src&&(r.src=w.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var T=t.alternate;if(T!==null){var W=T.memoizedState;if(W!==null){var H=W.dehydrated;H!==null&&ml(H)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(a(163))}pt||t.flags&512&&Ia(t)}catch(V){qe(t,t.return,V)}}if(t===e){ne=null;break}if(r=t.sibling,r!==null){r.return=t.return,ne=r;break}ne=t.return}}function Tc(e){for(;ne!==null;){var t=ne;if(t===e){ne=null;break}var r=t.sibling;if(r!==null){r.return=t.return,ne=r;break}ne=t.return}}function Mc(e){for(;ne!==null;){var t=ne;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{Qo(4,t)}catch(w){qe(t,r,w)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var o=t.return;try{n.componentDidMount()}catch(w){qe(t,o,w)}}var u=t.return;try{Ia(t)}catch(w){qe(t,u,w)}break;case 5:var f=t.return;try{Ia(t)}catch(w){qe(t,f,w)}}}catch(w){qe(t,t.return,w)}if(t===e){ne=null;break}var g=t.sibling;if(g!==null){g.return=t.return,ne=g;break}ne=t.return}}var D0=Math.ceil,Ko=te.ReactCurrentDispatcher,Ba=te.ReactCurrentOwner,Ft=te.ReactCurrentBatchConfig,Me=0,at=null,et=null,dt=0,Rt=0,$n=Mr(0),lt=0,Fl=null,cn=0,Yo=0,Va=0,Il=null,St=null,Wa=0,Qn=1/0,gr=null,Go=!1,Ha=null,Ur=null,Jo=!1,Ar=null,Xo=0,Ul=0,$a=null,qo=-1,Zo=0;function yt(){return(Me&6)!==0?ze():qo!==-1?qo:qo=ze()}function Br(e){return(e.mode&1)===0?1:(Me&2)!==0&&dt!==0?dt&-dt:y0.transition!==null?(Zo===0&&(Zo=Ps()),Zo):(e=Ie,e!==0||(e=window.event,e=e===void 0?16:Ds(e.type)),e)}function Kt(e,t,r,n){if(50<Ul)throw Ul=0,$a=null,Error(a(185));ul(e,r,n),((Me&2)===0||e!==at)&&(e===at&&((Me&2)===0&&(Yo|=r),lt===4&&Vr(e,dt)),kt(e,n),r===1&&Me===0&&(t.mode&1)===0&&(Qn=ze()+500,Co&&Dr()))}function kt(e,t){var r=e.callbackNode;yf(e,t);var n=ao(e,e===at?dt:0);if(n===0)r!==null&&ir(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&ir(r),t===1)e.tag===0?v0(Dc.bind(null,e)):wu(Dc.bind(null,e)),h0(function(){(Me&6)===0&&Dr()}),r=null;else{switch(Cs(n)){case 1:r=ar;break;case 4:r=er;break;case 16:r=sr;break;case 536870912:r=be;break;default:r=sr}r=Wc(r,zc.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function zc(e,t){if(qo=-1,Zo=0,(Me&6)!==0)throw Error(a(327));var r=e.callbackNode;if(Kn()&&e.callbackNode!==r)return null;var n=ao(e,e===at?dt:0);if(n===0)return null;if((n&30)!==0||(n&e.expiredLanes)!==0||t)t=ei(e,n);else{t=n;var o=Me;Me|=2;var u=Fc();(at!==e||dt!==t)&&(gr=null,Qn=ze()+500,fn(e,t));do try{I0();break}catch(g){Oc(e,g)}while(!0);ua(),Ko.current=u,Me=o,et!==null?t=0:(at=null,dt=0,t=lt)}if(t!==0){if(t===2&&(o=Ei(e),o!==0&&(n=o,t=Qa(e,o))),t===1)throw r=Fl,fn(e,0),Vr(e,n),kt(e,ze()),r;if(t===6)Vr(e,n);else{if(o=e.current.alternate,(n&30)===0&&!O0(o)&&(t=ei(e,n),t===2&&(u=Ei(e),u!==0&&(n=u,t=Qa(e,u))),t===1))throw r=Fl,fn(e,0),Vr(e,n),kt(e,ze()),r;switch(e.finishedWork=o,e.finishedLanes=n,t){case 0:case 1:throw Error(a(345));case 2:hn(e,St,gr);break;case 3:if(Vr(e,n),(n&130023424)===n&&(t=Wa+500-ze(),10<t)){if(ao(e,0)!==0)break;if(o=e.suspendedLanes,(o&n)!==n){yt(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=qi(hn.bind(null,e,St,gr),t);break}hn(e,St,gr);break;case 4:if(Vr(e,n),(n&4194240)===n)break;for(t=e.eventTimes,o=-1;0<n;){var f=31-De(n);u=1<<f,f=t[f],f>o&&(o=f),n&=~u}if(n=o,n=ze()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*D0(n/1960))-n,10<n){e.timeoutHandle=qi(hn.bind(null,e,St,gr),n);break}hn(e,St,gr);break;case 5:hn(e,St,gr);break;default:throw Error(a(329))}}}return kt(e,ze()),e.callbackNode===r?zc.bind(null,e):null}function Qa(e,t){var r=Il;return e.current.memoizedState.isDehydrated&&(fn(e,t).flags|=256),e=ei(e,t),e!==2&&(t=St,St=r,t!==null&&Ka(t)),e}function Ka(e){St===null?St=e:St.push.apply(St,e)}function O0(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var o=r[n],u=o.getSnapshot;o=o.value;try{if(!Vt(u(),o))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Vr(e,t){for(t&=~Va,t&=~Yo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-De(t),n=1<<r;e[r]=-1,t&=~n}}function Dc(e){if((Me&6)!==0)throw Error(a(327));Kn();var t=ao(e,0);if((t&1)===0)return kt(e,ze()),null;var r=ei(e,t);if(e.tag!==0&&r===2){var n=Ei(e);n!==0&&(t=n,r=Qa(e,n))}if(r===1)throw r=Fl,fn(e,0),Vr(e,t),kt(e,ze()),r;if(r===6)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,hn(e,St,gr),kt(e,ze()),null}function Ya(e,t){var r=Me;Me|=1;try{return e(t)}finally{Me=r,Me===0&&(Qn=ze()+500,Co&&Dr())}}function dn(e){Ar!==null&&Ar.tag===0&&(Me&6)===0&&Kn();var t=Me;Me|=1;var r=Ft.transition,n=Ie;try{if(Ft.transition=null,Ie=1,e)return e()}finally{Ie=n,Ft.transition=r,Me=t,(Me&6)===0&&Dr()}}function Ga(){Rt=$n.current,Qe($n)}function fn(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,f0(r)),et!==null)for(r=et.return;r!==null;){var n=r;switch(la(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Eo();break;case 3:Vn(),Qe(xt),Qe(ft),va();break;case 5:pa(n);break;case 4:Vn();break;case 13:Qe(Je);break;case 19:Qe(Je);break;case 10:ca(n.type._context);break;case 22:case 23:Ga()}r=r.return}if(at=e,et=e=Wr(e.current,null),dt=Rt=t,lt=0,Fl=null,Va=Yo=cn=0,St=Il=null,an!==null){for(t=0;t<an.length;t++)if(r=an[t],n=r.interleaved,n!==null){r.interleaved=null;var o=n.next,u=r.pending;if(u!==null){var f=u.next;u.next=o,n.next=f}r.pending=n}an=null}return e}function Oc(e,t){do{var r=et;try{if(ua(),Fo.current=Bo,Io){for(var n=Xe.memoizedState;n!==null;){var o=n.queue;o!==null&&(o.pending=null),n=n.next}Io=!1}if(un=0,it=nt=Xe=null,_l=!1,Tl=0,Ba.current=null,r===null||r.return===null){lt=1,Fl=t,et=null;break}e:{var u=e,f=r.return,g=r,w=t;if(t=dt,g.flags|=32768,w!==null&&typeof w=="object"&&typeof w.then=="function"){var T=w,W=g,H=W.tag;if((W.mode&1)===0&&(H===0||H===11||H===15)){var V=W.alternate;V?(W.updateQueue=V.updateQueue,W.memoizedState=V.memoizedState,W.lanes=V.lanes):(W.updateQueue=null,W.memoizedState=null)}var ee=ac(f);if(ee!==null){ee.flags&=-257,sc(ee,f,g,u,t),ee.mode&1&&ic(u,T,t),t=ee,w=T;var ie=t.updateQueue;if(ie===null){var se=new Set;se.add(w),t.updateQueue=se}else ie.add(w);break e}else{if((t&1)===0){ic(u,T,t),Ja();break e}w=Error(a(426))}}else if(Ye&&g.mode&1){var Ze=ac(f);if(Ze!==null){(Ze.flags&65536)===0&&(Ze.flags|=256),sc(Ze,f,g,u,t),aa(Wn(w,g));break e}}u=w=Wn(w,g),lt!==4&&(lt=2),Il===null?Il=[u]:Il.push(u),u=f;do{switch(u.tag){case 3:u.flags|=65536,t&=-t,u.lanes|=t;var b=lc(u,w,t);_u(u,b);break e;case 1:g=w;var N=u.type,R=u.stateNode;if((u.flags&128)===0&&(typeof N.getDerivedStateFromError=="function"||R!==null&&typeof R.componentDidCatch=="function"&&(Ur===null||!Ur.has(R)))){u.flags|=65536,t&=-t,u.lanes|=t;var Q=oc(u,g,t);_u(u,Q);break e}}u=u.return}while(u!==null)}Uc(r)}catch(ce){t=ce,et===r&&r!==null&&(et=r=r.return);continue}break}while(!0)}function Fc(){var e=Ko.current;return Ko.current=Bo,e===null?Bo:e}function Ja(){(lt===0||lt===3||lt===2)&&(lt=4),at===null||(cn&268435455)===0&&(Yo&268435455)===0||Vr(at,dt)}function ei(e,t){var r=Me;Me|=2;var n=Fc();(at!==e||dt!==t)&&(gr=null,fn(e,t));do try{F0();break}catch(o){Oc(e,o)}while(!0);if(ua(),Me=r,Ko.current=n,et!==null)throw Error(a(261));return at=null,dt=0,lt}function F0(){for(;et!==null;)Ic(et)}function I0(){for(;et!==null&&!en();)Ic(et)}function Ic(e){var t=Vc(e.alternate,e,Rt);e.memoizedProps=e.pendingProps,t===null?Uc(e):et=t,Ba.current=null}function Uc(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=R0(r,t,Rt),r!==null){et=r;return}}else{if(r=_0(r,t),r!==null){r.flags&=32767,et=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{lt=6,et=null;return}}if(t=t.sibling,t!==null){et=t;return}et=t=e}while(t!==null);lt===0&&(lt=5)}function hn(e,t,r){var n=Ie,o=Ft.transition;try{Ft.transition=null,Ie=1,U0(e,t,r,n)}finally{Ft.transition=o,Ie=n}return null}function U0(e,t,r,n){do Kn();while(Ar!==null);if((Me&6)!==0)throw Error(a(327));r=e.finishedWork;var o=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var u=r.lanes|r.childLanes;if(xf(e,u),e===at&&(et=at=null,dt=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||Jo||(Jo=!0,Wc(sr,function(){return Kn(),null})),u=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||u){u=Ft.transition,Ft.transition=null;var f=Ie;Ie=1;var g=Me;Me|=4,Ba.current=null,M0(e,r),Lc(r,e),o0(Ji),co=!!Gi,Ji=Gi=null,e.current=r,z0(r),Bt(),Me=g,Ie=f,Ft.transition=u}else e.current=r;if(Jo&&(Jo=!1,Ar=e,Xo=o),u=e.pendingLanes,u===0&&(Ur=null),tn(r.stateNode),kt(e,ze()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)o=t[r],n(o.value,{componentStack:o.stack,digest:o.digest});if(Go)throw Go=!1,e=Ha,Ha=null,e;return(Xo&1)!==0&&e.tag!==0&&Kn(),u=e.pendingLanes,(u&1)!==0?e===$a?Ul++:(Ul=0,$a=e):Ul=0,Dr(),null}function Kn(){if(Ar!==null){var e=Cs(Xo),t=Ft.transition,r=Ie;try{if(Ft.transition=null,Ie=16>e?16:e,Ar===null)var n=!1;else{if(e=Ar,Ar=null,Xo=0,(Me&6)!==0)throw Error(a(331));var o=Me;for(Me|=4,ne=e.current;ne!==null;){var u=ne,f=u.child;if((ne.flags&16)!==0){var g=u.deletions;if(g!==null){for(var w=0;w<g.length;w++){var T=g[w];for(ne=T;ne!==null;){var W=ne;switch(W.tag){case 0:case 11:case 15:Ol(8,W,u)}var H=W.child;if(H!==null)H.return=W,ne=H;else for(;ne!==null;){W=ne;var V=W.sibling,ee=W.return;if(Nc(W),W===T){ne=null;break}if(V!==null){V.return=ee,ne=V;break}ne=ee}}}var ie=u.alternate;if(ie!==null){var se=ie.child;if(se!==null){ie.child=null;do{var Ze=se.sibling;se.sibling=null,se=Ze}while(se!==null)}}ne=u}}if((u.subtreeFlags&2064)!==0&&f!==null)f.return=u,ne=f;else e:for(;ne!==null;){if(u=ne,(u.flags&2048)!==0)switch(u.tag){case 0:case 11:case 15:Ol(9,u,u.return)}var b=u.sibling;if(b!==null){b.return=u.return,ne=b;break e}ne=u.return}}var N=e.current;for(ne=N;ne!==null;){f=ne;var R=f.child;if((f.subtreeFlags&2064)!==0&&R!==null)R.return=f,ne=R;else e:for(f=N;ne!==null;){if(g=ne,(g.flags&2048)!==0)try{switch(g.tag){case 0:case 11:case 15:Qo(9,g)}}catch(ce){qe(g,g.return,ce)}if(g===f){ne=null;break e}var Q=g.sibling;if(Q!==null){Q.return=g.return,ne=Q;break e}ne=g.return}}if(Me=o,Dr(),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(Ae,e)}catch{}n=!0}return n}finally{Ie=r,Ft.transition=t}}return!1}function Ac(e,t,r){t=Wn(r,t),t=lc(e,t,1),e=Fr(e,t,1),t=yt(),e!==null&&(ul(e,1,t),kt(e,t))}function qe(e,t,r){if(e.tag===3)Ac(e,e,r);else for(;t!==null;){if(t.tag===3){Ac(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Ur===null||!Ur.has(n))){e=Wn(r,e),e=oc(t,e,1),t=Fr(t,e,1),e=yt(),t!==null&&(ul(t,1,e),kt(t,e));break}}t=t.return}}function A0(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=yt(),e.pingedLanes|=e.suspendedLanes&r,at===e&&(dt&r)===r&&(lt===4||lt===3&&(dt&130023424)===dt&&500>ze()-Wa?fn(e,0):Va|=r),kt(e,t)}function Bc(e,t){t===0&&((e.mode&1)===0?t=1:(t=io,io<<=1,(io&130023424)===0&&(io=4194304)));var r=yt();e=hr(e,t),e!==null&&(ul(e,t,r),kt(e,r))}function B0(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Bc(e,r)}function V0(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,o=e.memoizedState;o!==null&&(r=o.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(a(314))}n!==null&&n.delete(t),Bc(e,r)}var Vc;Vc=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||xt.current)jt=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return jt=!1,L0(e,t,r);jt=(e.flags&131072)!==0}else jt=!1,Ye&&(t.flags&1048576)!==0&&ju(t,Lo,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Ho(e,t),e=t.pendingProps;var o=Dn(t,ft.current);Bn(t,r),o=wa(null,t,n,e,o,r);var u=ja();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,wt(n)?(u=!0,Po(t)):u=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,ha(t),o.updater=Vo,t.stateNode=o,o._reactInternals=t,Ca(t,n,e,r),t=_a(null,t,n,!0,u,r)):(t.tag=0,Ye&&u&&na(t),vt(null,t,o,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Ho(e,t),e=t.pendingProps,o=n._init,n=o(n._payload),t.type=n,o=t.tag=H0(n),e=Ht(n,e),o){case 0:t=Ra(null,t,n,e,r);break e;case 1:t=mc(null,t,n,e,r);break e;case 11:t=uc(null,t,n,e,r);break e;case 14:t=cc(null,t,n,Ht(n.type,e),r);break e}throw Error(a(306,n,""))}return t;case 0:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:Ht(n,o),Ra(e,t,n,o,r);case 1:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:Ht(n,o),mc(e,t,n,o,r);case 3:e:{if(pc(t),e===null)throw Error(a(387));n=t.pendingProps,u=t.memoizedState,o=u.element,Ru(e,t),Do(t,n,null,r);var f=t.memoizedState;if(n=f.element,u.isDehydrated)if(u={element:n,isDehydrated:!1,cache:f.cache,pendingSuspenseBoundaries:f.pendingSuspenseBoundaries,transitions:f.transitions},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){o=Wn(Error(a(423)),t),t=gc(e,t,n,r,o);break e}else if(n!==o){o=Wn(Error(a(424)),t),t=gc(e,t,n,r,o);break e}else for(Lt=Tr(t.stateNode.containerInfo.firstChild),bt=t,Ye=!0,Wt=null,r=bu(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(In(),n===o){t=pr(e,t,r);break e}vt(e,t,n,r)}t=t.child}return t;case 5:return Mu(t),e===null&&ia(t),n=t.type,o=t.pendingProps,u=e!==null?e.memoizedProps:null,f=o.children,Xi(n,o)?f=null:u!==null&&Xi(n,u)&&(t.flags|=32),hc(e,t),vt(e,t,f,r),t.child;case 6:return e===null&&ia(t),null;case 13:return vc(e,t,r);case 4:return ma(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Un(t,null,n,r):vt(e,t,n,r),t.child;case 11:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:Ht(n,o),uc(e,t,n,o,r);case 7:return vt(e,t,t.pendingProps,r),t.child;case 8:return vt(e,t,t.pendingProps.children,r),t.child;case 12:return vt(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,o=t.pendingProps,u=t.memoizedProps,f=o.value,Ve(To,n._currentValue),n._currentValue=f,u!==null)if(Vt(u.value,f)){if(u.children===o.children&&!xt.current){t=pr(e,t,r);break e}}else for(u=t.child,u!==null&&(u.return=t);u!==null;){var g=u.dependencies;if(g!==null){f=u.child;for(var w=g.firstContext;w!==null;){if(w.context===n){if(u.tag===1){w=mr(-1,r&-r),w.tag=2;var T=u.updateQueue;if(T!==null){T=T.shared;var W=T.pending;W===null?w.next=w:(w.next=W.next,W.next=w),T.pending=w}}u.lanes|=r,w=u.alternate,w!==null&&(w.lanes|=r),da(u.return,r,t),g.lanes|=r;break}w=w.next}}else if(u.tag===10)f=u.type===t.type?null:u.child;else if(u.tag===18){if(f=u.return,f===null)throw Error(a(341));f.lanes|=r,g=f.alternate,g!==null&&(g.lanes|=r),da(f,r,t),f=u.sibling}else f=u.child;if(f!==null)f.return=u;else for(f=u;f!==null;){if(f===t){f=null;break}if(u=f.sibling,u!==null){u.return=f.return,f=u;break}f=f.return}u=f}vt(e,t,o.children,r),t=t.child}return t;case 9:return o=t.type,n=t.pendingProps.children,Bn(t,r),o=Dt(o),n=n(o),t.flags|=1,vt(e,t,n,r),t.child;case 14:return n=t.type,o=Ht(n,t.pendingProps),o=Ht(n.type,o),cc(e,t,n,o,r);case 15:return dc(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:Ht(n,o),Ho(e,t),t.tag=1,wt(n)?(e=!0,Po(t)):e=!1,Bn(t,r),rc(t,n,o),Ca(t,n,o,r),_a(null,t,n,!0,e,r);case 19:return xc(e,t,r);case 22:return fc(e,t,r)}throw Error(a(156,t.tag))};function Wc(e,t){return He(e,t)}function W0(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function It(e,t,r,n){return new W0(e,t,r,n)}function Xa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function H0(e){if(typeof e=="function")return Xa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ke)return 11;if(e===We)return 14}return 2}function Wr(e,t){var r=e.alternate;return r===null?(r=It(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ti(e,t,r,n,o,u){var f=2;if(n=e,typeof e=="function")Xa(e)&&(f=1);else if(typeof e=="string")f=5;else e:switch(e){case L:return mn(r.children,o,u,t);case B:f=8,o|=8;break;case q:return e=It(12,r,t,o|2),e.elementType=q,e.lanes=u,e;case Ce:return e=It(13,r,t,o),e.elementType=Ce,e.lanes=u,e;case Te:return e=It(19,r,t,o),e.elementType=Te,e.lanes=u,e;case ue:return ri(r,o,u,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Y:f=10;break e;case Se:f=9;break e;case ke:f=11;break e;case We:f=14;break e;case Ke:f=16,n=null;break e}throw Error(a(130,e==null?e:typeof e,""))}return t=It(f,r,t,o),t.elementType=e,t.type=n,t.lanes=u,t}function mn(e,t,r,n){return e=It(7,e,n,t),e.lanes=r,e}function ri(e,t,r,n){return e=It(22,e,n,t),e.elementType=ue,e.lanes=r,e.stateNode={isHidden:!1},e}function qa(e,t,r){return e=It(6,e,null,t),e.lanes=r,e}function Za(e,t,r){return t=It(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function $0(e,t,r,n,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Pi(0),this.expirationTimes=Pi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pi(0),this.identifierPrefix=n,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function es(e,t,r,n,o,u,f,g,w){return e=new $0(e,t,r,g,w),t===1?(t=1,u===!0&&(t|=8)):t=0,u=It(3,null,null,t),e.current=u,u.stateNode=e,u.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},ha(u),e}function Q0(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ge,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function Hc(e){if(!e)return zr;e=e._reactInternals;e:{if(le(e)!==e||e.tag!==1)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(wt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(a(171))}if(e.tag===1){var r=e.type;if(wt(r))return yu(e,r,t)}return t}function $c(e,t,r,n,o,u,f,g,w){return e=es(r,n,!0,e,o,u,f,g,w),e.context=Hc(null),r=e.current,n=yt(),o=Br(r),u=mr(n,o),u.callback=t??null,Fr(r,u,o),e.current.lanes=o,ul(e,o,n),kt(e,n),e}function ni(e,t,r,n){var o=t.current,u=yt(),f=Br(o);return r=Hc(r),t.context===null?t.context=r:t.pendingContext=r,t=mr(u,f),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=Fr(o,t,f),e!==null&&(Kt(e,o,f,u),zo(e,o,f)),f}function li(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Qc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function ts(e,t){Qc(e,t),(e=e.alternate)&&Qc(e,t)}function K0(){return null}var Kc=typeof reportError=="function"?reportError:function(e){console.error(e)};function rs(e){this._internalRoot=e}oi.prototype.render=rs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(a(409));ni(e,t,null,null)},oi.prototype.unmount=rs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dn(function(){ni(null,e,null,null)}),t[ur]=null}};function oi(e){this._internalRoot=e}oi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rs();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Lr.length&&t!==0&&t<Lr[r].priority;r++);Lr.splice(r,0,e),r===0&&Ms(e)}};function ns(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ii(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Yc(){}function Y0(e,t,r,n,o){if(o){if(typeof n=="function"){var u=n;n=function(){var T=li(f);u.call(T)}}var f=$c(t,n,e,0,null,!1,!1,"",Yc);return e._reactRootContainer=f,e[ur]=f.current,kl(e.nodeType===8?e.parentNode:e),dn(),f}for(;o=e.lastChild;)e.removeChild(o);if(typeof n=="function"){var g=n;n=function(){var T=li(w);g.call(T)}}var w=es(e,0,!1,null,null,!1,!1,"",Yc);return e._reactRootContainer=w,e[ur]=w.current,kl(e.nodeType===8?e.parentNode:e),dn(function(){ni(t,w,r,n)}),w}function ai(e,t,r,n,o){var u=r._reactRootContainer;if(u){var f=u;if(typeof o=="function"){var g=o;o=function(){var w=li(f);g.call(w)}}ni(t,f,e,o)}else f=Y0(r,t,e,o,n);return li(f)}bs=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=sl(t.pendingLanes);r!==0&&(Ci(t,r|1),kt(t,ze()),(Me&6)===0&&(Qn=ze()+500,Dr()))}break;case 13:dn(function(){var n=hr(e,1);if(n!==null){var o=yt();Kt(n,e,1,o)}}),ts(e,1)}},bi=function(e){if(e.tag===13){var t=hr(e,134217728);if(t!==null){var r=yt();Kt(t,e,134217728,r)}ts(e,134217728)}},Ls=function(e){if(e.tag===13){var t=Br(e),r=hr(e,t);if(r!==null){var n=yt();Kt(r,e,t,n)}ts(e,t)}},Rs=function(){return Ie},_s=function(e,t){var r=Ie;try{return Ie=e,t()}finally{Ie=r}},Nn=function(e,t,r){switch(t){case"input":if(el(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var o=No(n);if(!o)throw Error(a(90));ot(n),el(n,o)}}}break;case"textarea":Jt(e,r);break;case"select":t=r.value,t!=null&&wr(e,!!r.multiple,t,!1)}},il=Ya,qr=dn;var G0={usingClientEntryPoint:!1,Events:[Pl,Mn,No,ol,ro,Ya]},Al={findFiberByHostInstance:rn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},J0={bundleType:Al.bundleType,version:Al.version,rendererPackageName:Al.rendererPackageName,rendererConfig:Al.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:te.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Fe(e),e===null?null:e.stateNode},findFiberByHostInstance:Al.findFiberByHostInstance||K0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!si.isDisabled&&si.supportsFiber)try{Ae=si.inject(J0),ut=si}catch{}}return Nt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=G0,Nt.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ns(t))throw Error(a(200));return Q0(e,t,null,r)},Nt.createRoot=function(e,t){if(!ns(e))throw Error(a(299));var r=!1,n="",o=Kc;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=es(e,1,!1,null,null,r,!1,n,o),e[ur]=t.current,kl(e.nodeType===8?e.parentNode:e),new rs(t)},Nt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(a(188)):(e=Object.keys(e).join(","),Error(a(268,e)));return e=Fe(t),e=e===null?null:e.stateNode,e},Nt.flushSync=function(e){return dn(e)},Nt.hydrate=function(e,t,r){if(!ii(t))throw Error(a(200));return ai(null,e,t,!0,r)},Nt.hydrateRoot=function(e,t,r){if(!ns(e))throw Error(a(405));var n=r!=null&&r.hydratedSources||null,o=!1,u="",f=Kc;if(r!=null&&(r.unstable_strictMode===!0&&(o=!0),r.identifierPrefix!==void 0&&(u=r.identifierPrefix),r.onRecoverableError!==void 0&&(f=r.onRecoverableError)),t=$c(t,null,e,1,r??null,o,!1,u,f),e[ur]=t.current,kl(e),n)for(e=0;e<n.length;e++)r=n[e],o=r._getVersion,o=o(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,o]:t.mutableSourceEagerHydrationData.push(r,o);return new oi(t)},Nt.render=function(e,t,r){if(!ii(t))throw Error(a(200));return ai(null,e,t,!1,r)},Nt.unmountComponentAtNode=function(e){if(!ii(e))throw Error(a(40));return e._reactRootContainer?(dn(function(){ai(null,null,e,!1,function(){e._reactRootContainer=null,e[ur]=null})}),!0):!1},Nt.unstable_batchedUpdates=Ya,Nt.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!ii(r))throw Error(a(200));if(e==null||e._reactInternals===void 0)throw Error(a(38));return ai(e,t,r,!1,n)},Nt.version="18.3.1-next-f1338f8080-20240426",Nt}var rd;function Id(){if(rd)return is.exports;rd=1;function l(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l)}catch(s){console.error(s)}}return l(),is.exports=lh(),is.exports}var nd;function oh(){if(nd)return ui;nd=1;var l=Id();return ui.createRoot=l.createRoot,ui.hydrateRoot=l.hydrateRoot,ui}var ih=oh(),Ud=Id();const ah=ws(Ud),sh=Fd({__proto__:null,default:ah},[Ud]);/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ge(){return Ge=Object.assign?Object.assign.bind():function(l){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(l[c]=a[c])}return l},Ge.apply(this,arguments)}var tt;(function(l){l.Pop="POP",l.Push="PUSH",l.Replace="REPLACE"})(tt||(tt={}));const ld="popstate";function uh(l){l===void 0&&(l={});function s(c,d){let{pathname:h,search:m,hash:v}=c.location;return Ql("",{pathname:h,search:m,hash:v},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function a(c,d){return typeof d=="string"?d:yn(d)}return dh(s,a,null,l)}function Pe(l,s){if(l===!1||l===null||typeof l>"u")throw new Error(s)}function Gn(l,s){if(!l){typeof console<"u"&&console.warn(s);try{throw new Error(s)}catch{}}}function ch(){return Math.random().toString(36).substr(2,8)}function od(l,s){return{usr:l.state,key:l.key,idx:s}}function Ql(l,s,a,c){return a===void 0&&(a=null),Ge({pathname:typeof l=="string"?l:l.pathname,search:"",hash:""},typeof s=="string"?Kr(s):s,{state:a,key:s&&s.key||c||ch()})}function yn(l){let{pathname:s="/",search:a="",hash:c=""}=l;return a&&a!=="?"&&(s+=a.charAt(0)==="?"?a:"?"+a),c&&c!=="#"&&(s+=c.charAt(0)==="#"?c:"#"+c),s}function Kr(l){let s={};if(l){let a=l.indexOf("#");a>=0&&(s.hash=l.substr(a),l=l.substr(0,a));let c=l.indexOf("?");c>=0&&(s.search=l.substr(c),l=l.substr(0,c)),l&&(s.pathname=l)}return s}function dh(l,s,a,c){c===void 0&&(c={});let{window:d=document.defaultView,v5Compat:h=!1}=c,m=d.history,v=tt.Pop,p=null,S=k();S==null&&(S=0,m.replaceState(Ge({},m.state,{idx:S}),""));function k(){return(m.state||{idx:null}).idx}function j(){v=tt.Pop;let _=k(),Z=_==null?null:_-S;S=_,p&&p({action:v,location:F.location,delta:Z})}function P(_,Z){v=tt.Push;let $=Ql(F.location,_,Z);S=k()+1;let K=od($,S),te=F.createHref($);try{m.pushState(K,"",te)}catch(X){if(X instanceof DOMException&&X.name==="DataCloneError")throw X;d.location.assign(te)}h&&p&&p({action:v,location:F.location,delta:1})}function D(_,Z){v=tt.Replace;let $=Ql(F.location,_,Z);S=k();let K=od($,S),te=F.createHref($);m.replaceState(K,"",te),h&&p&&p({action:v,location:F.location,delta:0})}function U(_){let Z=d.location.origin!=="null"?d.location.origin:d.location.href,$=typeof _=="string"?_:yn(_);return $=$.replace(/ $/,"%20"),Pe(Z,"No window.location.(origin|href) available to create URL for href: "+$),new URL($,Z)}let F={get action(){return v},get location(){return l(d,m)},listen(_){if(p)throw new Error("A history only accepts one active listener");return d.addEventListener(ld,j),p=_,()=>{d.removeEventListener(ld,j),p=null}},createHref(_){return s(d,_)},createURL:U,encodeLocation(_){let Z=U(_);return{pathname:Z.pathname,search:Z.search,hash:Z.hash}},push:P,replace:D,go(_){return m.go(_)}};return F}var Ue;(function(l){l.data="data",l.deferred="deferred",l.redirect="redirect",l.error="error"})(Ue||(Ue={}));const fh=new Set(["lazy","caseSensitive","path","id","index","children"]);function hh(l){return l.index===!0}function hi(l,s,a,c){return a===void 0&&(a=[]),c===void 0&&(c={}),l.map((d,h)=>{let m=[...a,String(h)],v=typeof d.id=="string"?d.id:m.join("-");if(Pe(d.index!==!0||!d.children,"Cannot specify children on an index route"),Pe(!c[v],'Found a route id collision on id "'+v+`".  Route id's must be globally unique within Data Router usages`),hh(d)){let p=Ge({},d,s(d),{id:v});return c[v]=p,p}else{let p=Ge({},d,s(d),{id:v,children:void 0});return c[v]=p,d.children&&(p.children=hi(d.children,s,m,c)),p}})}function pn(l,s,a){return a===void 0&&(a="/"),fi(l,s,a,!1)}function fi(l,s,a,c){let d=typeof s=="string"?Kr(s):s,h=Jn(d.pathname||"/",a);if(h==null)return null;let m=Ad(l);ph(m);let v=null;for(let p=0;v==null&&p<m.length;++p){let S=Ph(h);v=Nh(m[p],S,c)}return v}function mh(l,s){let{route:a,pathname:c,params:d}=l;return{id:a.id,pathname:c,params:d,data:s[a.id],handle:a.handle}}function Ad(l,s,a,c){s===void 0&&(s=[]),a===void 0&&(a=[]),c===void 0&&(c="");let d=(h,m,v)=>{let p={relativePath:v===void 0?h.path||"":v,caseSensitive:h.caseSensitive===!0,childrenIndex:m,route:h};p.relativePath.startsWith("/")&&(Pe(p.relativePath.startsWith(c),'Absolute route path "'+p.relativePath+'" nested under path '+('"'+c+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),p.relativePath=p.relativePath.slice(c.length));let S=yr([c,p.relativePath]),k=a.concat(p);h.children&&h.children.length>0&&(Pe(h.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+S+'".')),Ad(h.children,s,k,S)),!(h.path==null&&!h.index)&&s.push({path:S,score:Sh(S,h.index),routesMeta:k})};return l.forEach((h,m)=>{var v;if(h.path===""||!((v=h.path)!=null&&v.includes("?")))d(h,m);else for(let p of Bd(h.path))d(h,m,p)}),s}function Bd(l){let s=l.split("/");if(s.length===0)return[];let[a,...c]=s,d=a.endsWith("?"),h=a.replace(/\?$/,"");if(c.length===0)return d?[h,""]:[h];let m=Bd(c.join("/")),v=[];return v.push(...m.map(p=>p===""?h:[h,p].join("/"))),d&&v.push(...m),v.map(p=>l.startsWith("/")&&p===""?"/":p)}function ph(l){l.sort((s,a)=>s.score!==a.score?a.score-s.score:kh(s.routesMeta.map(c=>c.childrenIndex),a.routesMeta.map(c=>c.childrenIndex)))}const gh=/^:[\w-]+$/,vh=3,yh=2,xh=1,wh=10,jh=-2,id=l=>l==="*";function Sh(l,s){let a=l.split("/"),c=a.length;return a.some(id)&&(c+=jh),s&&(c+=yh),a.filter(d=>!id(d)).reduce((d,h)=>d+(gh.test(h)?vh:h===""?xh:wh),c)}function kh(l,s){return l.length===s.length&&l.slice(0,-1).every((c,d)=>c===s[d])?l[l.length-1]-s[s.length-1]:0}function Nh(l,s,a){a===void 0&&(a=!1);let{routesMeta:c}=l,d={},h="/",m=[];for(let v=0;v<c.length;++v){let p=c[v],S=v===c.length-1,k=h==="/"?s:s.slice(h.length)||"/",j=ad({path:p.relativePath,caseSensitive:p.caseSensitive,end:S},k),P=p.route;if(!j&&S&&a&&!c[c.length-1].route.index&&(j=ad({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},k)),!j)return null;Object.assign(d,j.params),m.push({params:d,pathname:yr([h,j.pathname]),pathnameBase:Lh(yr([h,j.pathnameBase])),route:P}),j.pathnameBase!=="/"&&(h=yr([h,j.pathnameBase]))}return m}function ad(l,s){typeof l=="string"&&(l={path:l,caseSensitive:!1,end:!0});let[a,c]=Eh(l.path,l.caseSensitive,l.end),d=s.match(a);if(!d)return null;let h=d[0],m=h.replace(/(.)\/+$/,"$1"),v=d.slice(1);return{params:c.reduce((S,k,j)=>{let{paramName:P,isOptional:D}=k;if(P==="*"){let F=v[j]||"";m=h.slice(0,h.length-F.length).replace(/(.)\/+$/,"$1")}const U=v[j];return D&&!U?S[P]=void 0:S[P]=(U||"").replace(/%2F/g,"/"),S},{}),pathname:h,pathnameBase:m,pattern:l}}function Eh(l,s,a){s===void 0&&(s=!1),a===void 0&&(a=!0),Gn(l==="*"||!l.endsWith("*")||l.endsWith("/*"),'Route path "'+l+'" will be treated as if it were '+('"'+l.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+l.replace(/\*$/,"/*")+'".'));let c=[],d="^"+l.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(m,v,p)=>(c.push({paramName:v,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return l.endsWith("*")?(c.push({paramName:"*"}),d+=l==="*"||l==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):a?d+="\\/*$":l!==""&&l!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,s?void 0:"i"),c]}function Ph(l){try{return l.split("/").map(s=>decodeURIComponent(s).replace(/\//g,"%2F")).join("/")}catch(s){return Gn(!1,'The URL path "'+l+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+s+").")),l}}function Jn(l,s){if(s==="/")return l;if(!l.toLowerCase().startsWith(s.toLowerCase()))return null;let a=s.endsWith("/")?s.length-1:s.length,c=l.charAt(a);return c&&c!=="/"?null:l.slice(a)||"/"}function Ch(l,s){s===void 0&&(s="/");let{pathname:a,search:c="",hash:d=""}=typeof l=="string"?Kr(l):l;return{pathname:a?a.startsWith("/")?a:bh(a,s):s,search:Rh(c),hash:_h(d)}}function bh(l,s){let a=s.replace(/\/+$/,"").split("/");return l.split("/").forEach(d=>{d===".."?a.length>1&&a.pop():d!=="."&&a.push(d)}),a.length>1?a.join("/"):"/"}function us(l,s,a,c){return"Cannot include a '"+l+"' character in a manually specified "+("`to."+s+"` field ["+JSON.stringify(c)+"].  Please separate it out to the ")+("`to."+a+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Vd(l){return l.filter((s,a)=>a===0||s.route.path&&s.route.path.length>0)}function yi(l,s){let a=Vd(l);return s?a.map((c,d)=>d===a.length-1?c.pathname:c.pathnameBase):a.map(c=>c.pathnameBase)}function xi(l,s,a,c){c===void 0&&(c=!1);let d;typeof l=="string"?d=Kr(l):(d=Ge({},l),Pe(!d.pathname||!d.pathname.includes("?"),us("?","pathname","search",d)),Pe(!d.pathname||!d.pathname.includes("#"),us("#","pathname","hash",d)),Pe(!d.search||!d.search.includes("#"),us("#","search","hash",d)));let h=l===""||d.pathname==="",m=h?"/":d.pathname,v;if(m==null)v=a;else{let j=s.length-1;if(!c&&m.startsWith("..")){let P=m.split("/");for(;P[0]==="..";)P.shift(),j-=1;d.pathname=P.join("/")}v=j>=0?s[j]:"/"}let p=Ch(d,v),S=m&&m!=="/"&&m.endsWith("/"),k=(h||m===".")&&a.endsWith("/");return!p.pathname.endsWith("/")&&(S||k)&&(p.pathname+="/"),p}const yr=l=>l.join("/").replace(/\/\/+/g,"/"),Lh=l=>l.replace(/\/+$/,"").replace(/^\/*/,"/"),Rh=l=>!l||l==="?"?"":l.startsWith("?")?l:"?"+l,_h=l=>!l||l==="#"?"":l.startsWith("#")?l:"#"+l;class mi{constructor(s,a,c,d){d===void 0&&(d=!1),this.status=s,this.statusText=a||"",this.internal=d,c instanceof Error?(this.data=c.toString(),this.error=c):this.data=c}}function Kl(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.internal=="boolean"&&"data"in l}const Wd=["post","put","patch","delete"],Th=new Set(Wd),Mh=["get",...Wd],zh=new Set(Mh),Dh=new Set([301,302,303,307,308]),Oh=new Set([307,308]),cs={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Fh={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Vl={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},js=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ih=l=>({hasErrorBoundary:!!l.hasErrorBoundary}),Hd="remix-router-transitions";function Uh(l){const s=l.window?l.window:typeof window<"u"?window:void 0,a=typeof s<"u"&&typeof s.document<"u"&&typeof s.document.createElement<"u",c=!a;Pe(l.routes.length>0,"You must provide a non-empty routes array to createRouter");let d;if(l.mapRouteProperties)d=l.mapRouteProperties;else if(l.detectErrorBoundary){let x=l.detectErrorBoundary;d=E=>({hasErrorBoundary:x(E)})}else d=Ih;let h={},m=hi(l.routes,d,void 0,h),v,p=l.basename||"/",S=l.dataStrategy||Wh,k=l.patchRoutesOnNavigation,j=Ge({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},l.future),P=null,D=new Set,U=null,F=null,_=null,Z=l.hydrationData!=null,$=pn(m,l.history.location,p),K=!1,te=null;if($==null&&!k){let x=Et(404,{pathname:l.history.location.pathname}),{matches:E,route:z}=yd(m);$=E,te={[z.id]:x}}$&&!l.hydrationData&&qr($,m,l.history.location.pathname).active&&($=null);let X;if($)if($.some(x=>x.route.lazy))X=!1;else if(!$.some(x=>x.route.loader))X=!0;else if(j.v7_partialHydration){let x=l.hydrationData?l.hydrationData.loaderData:null,E=l.hydrationData?l.hydrationData.errors:null;if(E){let z=$.findIndex(A=>E[A.route.id]!==void 0);X=$.slice(0,z+1).every(A=>!vs(A.route,x,E))}else X=$.every(z=>!vs(z.route,x,E))}else X=l.hydrationData!=null;else if(X=!1,$=[],j.v7_partialHydration){let x=qr(null,m,l.history.location.pathname);x.active&&x.matches&&(K=!0,$=x.matches)}let ge,L={historyAction:l.history.action,location:l.history.location,matches:$,initialized:X,navigation:cs,restoreScrollPosition:l.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:l.hydrationData&&l.hydrationData.loaderData||{},actionData:l.hydrationData&&l.hydrationData.actionData||null,errors:l.hydrationData&&l.hydrationData.errors||te,fetchers:new Map,blockers:new Map},B=tt.Pop,q=!1,Y,Se=!1,ke=new Map,Ce=null,Te=!1,We=!1,Ke=[],ue=new Set,M=new Map,J=0,I=-1,y=new Map,O=new Set,fe=new Map,xe=new Map,ve=new Set,je=new Map,Le=new Map,Re;function Be(){if(P=l.history.listen(x=>{let{action:E,location:z,delta:A}=x;if(Re){Re(),Re=void 0;return}Gn(Le.size===0||A!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let G=Nn({currentLocation:L.location,nextLocation:z,historyAction:E});if(G&&A!=null){let de=new Promise(pe=>{Re=pe});l.history.go(A*-1),kr(G,{state:"blocked",location:z,proceed(){kr(G,{state:"proceeding",proceed:void 0,reset:void 0,location:z}),de.then(()=>l.history.go(A))},reset(){let pe=new Map(L.blockers);pe.set(G,Vl),ot({blockers:pe})}});return}return Gt(E,z)}),a){nm(s,ke);let x=()=>lm(s,ke);s.addEventListener("pagehide",x),Ce=()=>s.removeEventListener("pagehide",x)}return L.initialized||Gt(tt.Pop,L.location,{initialHydration:!0}),ge}function gt(){P&&P(),Ce&&Ce(),D.clear(),Y&&Y.abort(),L.fetchers.forEach((x,E)=>Xt(E)),L.blockers.forEach((x,E)=>kn(E))}function xn(x){return D.add(x),()=>D.delete(x)}function ot(x,E){E===void 0&&(E={}),L=Ge({},L,x);let z=[],A=[];j.v7_fetcherPersist&&L.fetchers.forEach((G,de)=>{G.state==="idle"&&(ve.has(de)?A.push(de):z.push(de))}),ve.forEach(G=>{!L.fetchers.has(G)&&!M.has(G)&&A.push(G)}),[...D].forEach(G=>G(L,{deletedFetchers:A,viewTransitionOpts:E.viewTransitionOpts,flushSync:E.flushSync===!0})),j.v7_fetcherPersist?(z.forEach(G=>L.fetchers.delete(G)),A.forEach(G=>Xt(G))):A.forEach(G=>ve.delete(G))}function Ut(x,E,z){var A,G;let{flushSync:de}=z===void 0?{}:z,pe=L.actionData!=null&&L.navigation.formMethod!=null&&Yt(L.navigation.formMethod)&&L.navigation.state==="loading"&&((A=x.state)==null?void 0:A._isRedirect)!==!0,oe;E.actionData?Object.keys(E.actionData).length>0?oe=E.actionData:oe=null:pe?oe=L.actionData:oe=null;let ae=E.loaderData?gd(L.loaderData,E.loaderData,E.matches||[],E.errors):L.loaderData,re=L.blockers;re.size>0&&(re=new Map(re),re.forEach((Ee,rt)=>re.set(rt,Vl)));let le=q===!0||L.navigation.formMethod!=null&&Yt(L.navigation.formMethod)&&((G=x.state)==null?void 0:G._isRedirect)!==!0;v&&(m=v,v=void 0),Te||B===tt.Pop||(B===tt.Push?l.history.push(x,x.state):B===tt.Replace&&l.history.replace(x,x.state));let we;if(B===tt.Pop){let Ee=ke.get(L.location.pathname);Ee&&Ee.has(x.pathname)?we={currentLocation:L.location,nextLocation:x}:ke.has(x.pathname)&&(we={currentLocation:x,nextLocation:L.location})}else if(Se){let Ee=ke.get(L.location.pathname);Ee?Ee.add(x.pathname):(Ee=new Set([x.pathname]),ke.set(L.location.pathname,Ee)),we={currentLocation:L.location,nextLocation:x}}ot(Ge({},E,{actionData:oe,loaderData:ae,historyAction:B,location:x,initialized:!0,navigation:cs,revalidation:"idle",restoreScrollPosition:il(x,E.matches||L.matches),preventScrollReset:le,blockers:re}),{viewTransitionOpts:we,flushSync:de===!0}),B=tt.Pop,q=!1,Se=!1,Te=!1,We=!1,Ke=[]}async function wn(x,E){if(typeof x=="number"){l.history.go(x);return}let z=gs(L.location,L.matches,p,j.v7_prependBasename,x,j.v7_relativeSplatPath,E==null?void 0:E.fromRouteId,E==null?void 0:E.relative),{path:A,submission:G,error:de}=sd(j.v7_normalizeFormMethod,!1,z,E),pe=L.location,oe=Ql(L.location,A,E&&E.state);oe=Ge({},oe,l.history.encodeLocation(oe));let ae=E&&E.replace!=null?E.replace:void 0,re=tt.Push;ae===!0?re=tt.Replace:ae===!1||G!=null&&Yt(G.formMethod)&&G.formAction===L.location.pathname+L.location.search&&(re=tt.Replace);let le=E&&"preventScrollReset"in E?E.preventScrollReset===!0:void 0,we=(E&&E.flushSync)===!0,Ee=Nn({currentLocation:pe,nextLocation:oe,historyAction:re});if(Ee){kr(Ee,{state:"blocked",location:oe,proceed(){kr(Ee,{state:"proceeding",proceed:void 0,reset:void 0,location:oe}),wn(x,E)},reset(){let rt=new Map(L.blockers);rt.set(Ee,Vl),ot({blockers:rt})}});return}return await Gt(re,oe,{submission:G,pendingError:de,preventScrollReset:le,replace:E&&E.replace,enableViewTransition:E&&E.viewTransition,flushSync:we})}function Xl(){if(Xr(),ot({revalidation:"loading"}),L.navigation.state!=="submitting"){if(L.navigation.state==="idle"){Gt(L.historyAction,L.location,{startUninterruptedRevalidation:!0});return}Gt(B||L.historyAction,L.navigation.location,{overrideNavigation:L.navigation,enableViewTransition:Se===!0})}}async function Gt(x,E,z){Y&&Y.abort(),Y=null,B=x,Te=(z&&z.startUninterruptedRevalidation)===!0,ro(L.location,L.matches),q=(z&&z.preventScrollReset)===!0,Se=(z&&z.enableViewTransition)===!0;let A=v||m,G=z&&z.overrideNavigation,de=z!=null&&z.initialHydration&&L.matches&&L.matches.length>0&&!K?L.matches:pn(A,E,p),pe=(z&&z.flushSync)===!0;if(de&&L.initialized&&!We&&Gh(L.location,E)&&!(z&&z.submission&&Yt(z.submission.formMethod))){Ut(E,{matches:de},{flushSync:pe});return}let oe=qr(de,A,E.pathname);if(oe.active&&oe.matches&&(de=oe.matches),!de){let{error:Fe,notFoundMatches:_e,route:He}=qt(E.pathname);Ut(E,{matches:_e,loaderData:{},errors:{[He.id]:Fe}},{flushSync:pe});return}Y=new AbortController;let ae=Yn(l.history,E,Y.signal,z&&z.submission),re;if(z&&z.pendingError)re=[gn(de).route.id,{type:Ue.error,error:z.pendingError}];else if(z&&z.submission&&Yt(z.submission.formMethod)){let Fe=await el(ae,E,z.submission,de,oe.active,{replace:z.replace,flushSync:pe});if(Fe.shortCircuited)return;if(Fe.pendingActionResult){let[_e,He]=Fe.pendingActionResult;if(_t(He)&&Kl(He.error)&&He.error.status===404){Y=null,Ut(E,{matches:Fe.matches,loaderData:{},errors:{[_e]:He.error}});return}}de=Fe.matches||de,re=Fe.pendingActionResult,G=ds(E,z.submission),pe=!1,oe.active=!1,ae=Yn(l.history,ae.url,ae.signal)}let{shortCircuited:le,matches:we,loaderData:Ee,errors:rt}=await ql(ae,E,de,oe.active,G,z&&z.submission,z&&z.fetcherSubmission,z&&z.replace,z&&z.initialHydration===!0,pe,re);le||(Y=null,Ut(E,Ge({matches:we||de},vd(re),{loaderData:Ee,errors:rt})))}async function el(x,E,z,A,G,de){de===void 0&&(de={}),Xr();let pe=tm(E,z);if(ot({navigation:pe},{flushSync:de.flushSync===!0}),G){let re=await Nr(A,E.pathname,x.signal);if(re.type==="aborted")return{shortCircuited:!0};if(re.type==="error"){let le=gn(re.partialMatches).route.id;return{matches:re.partialMatches,pendingActionResult:[le,{type:Ue.error,error:re.error}]}}else if(re.matches)A=re.matches;else{let{notFoundMatches:le,error:we,route:Ee}=qt(E.pathname);return{matches:le,pendingActionResult:[Ee.id,{type:Ue.error,error:we}]}}}let oe,ae=$l(A,E);if(!ae.route.action&&!ae.route.lazy)oe={type:Ue.error,error:Et(405,{method:x.method,pathname:E.pathname,routeId:ae.route.id})};else if(oe=(await jr("action",L,x,[ae],A,null))[ae.route.id],x.signal.aborted)return{shortCircuited:!0};if(vn(oe)){let re;return de&&de.replace!=null?re=de.replace:re=hd(oe.response.headers.get("Location"),new URL(x.url),p)===L.location.pathname+L.location.search,await Jt(x,oe,!0,{submission:z,replace:re}),{shortCircuited:!0}}if(Qr(oe))throw Et(400,{type:"defer-action"});if(_t(oe)){let re=gn(A,ae.route.id);return(de&&de.replace)!==!0&&(B=tt.Push),{matches:A,pendingActionResult:[re.route.id,oe]}}return{matches:A,pendingActionResult:[ae.route.id,oe]}}async function ql(x,E,z,A,G,de,pe,oe,ae,re,le){let we=G||ds(E,de),Ee=de||pe||wd(we),rt=!Te&&(!j.v7_partialHydration||!ae);if(A){if(rt){let Ae=jn(le);ot(Ge({navigation:we},Ae!==void 0?{actionData:Ae}:{}),{flushSync:re})}let be=await Nr(z,E.pathname,x.signal);if(be.type==="aborted")return{shortCircuited:!0};if(be.type==="error"){let Ae=gn(be.partialMatches).route.id;return{matches:be.partialMatches,loaderData:{},errors:{[Ae]:be.error}}}else if(be.matches)z=be.matches;else{let{error:Ae,notFoundMatches:ut,route:tn}=qt(E.pathname);return{matches:ut,loaderData:{},errors:{[tn.id]:Ae}}}}let Fe=v||m,[_e,He]=cd(l.history,L,z,Ee,E,j.v7_partialHydration&&ae===!0,j.v7_skipActionErrorRevalidation,We,Ke,ue,ve,fe,O,Fe,p,le);if(Zt(be=>!(z&&z.some(Ae=>Ae.route.id===be))||_e&&_e.some(Ae=>Ae.route.id===be)),I=++J,_e.length===0&&He.length===0){let be=eo();return Ut(E,Ge({matches:z,loaderData:{},errors:le&&_t(le[1])?{[le[0]]:le[1].error}:null},vd(le),be?{fetchers:new Map(L.fetchers)}:{}),{flushSync:re}),{shortCircuited:!0}}if(rt){let be={};if(!A){be.navigation=we;let Ae=jn(le);Ae!==void 0&&(be.actionData=Ae)}He.length>0&&(be.fetchers=Jr(He)),ot(be,{flushSync:re})}He.forEach(be=>{At(be.key),be.controller&&M.set(be.key,be.controller)});let ir=()=>He.forEach(be=>At(be.key));Y&&Y.signal.addEventListener("abort",ir);let{loaderResults:en,fetcherResults:Bt}=await rl(L,z,_e,He,x);if(x.signal.aborted)return{shortCircuited:!0};Y&&Y.signal.removeEventListener("abort",ir),He.forEach(be=>M.delete(be.key));let ze=ci(en);if(ze)return await Jt(x,ze.result,!0,{replace:oe}),{shortCircuited:!0};if(ze=ci(Bt),ze)return O.add(ze.key),await Jt(x,ze.result,!0,{replace:oe}),{shortCircuited:!0};let{loaderData:al,errors:ar}=pd(L,z,en,le,He,Bt,je);je.forEach((be,Ae)=>{be.subscribe(ut=>{(ut||be.done)&&je.delete(Ae)})}),j.v7_partialHydration&&ae&&L.errors&&(ar=Ge({},L.errors,ar));let er=eo(),sr=Sn(I),En=er||sr||He.length>0;return Ge({matches:z,loaderData:al,errors:ar},En?{fetchers:new Map(L.fetchers)}:{})}function jn(x){if(x&&!_t(x[1]))return{[x[0]]:x[1].data};if(L.actionData)return Object.keys(L.actionData).length===0?null:L.actionData}function Jr(x){return x.forEach(E=>{let z=L.fetchers.get(E.key),A=Wl(void 0,z?z.data:void 0);L.fetchers.set(E.key,A)}),new Map(L.fetchers)}function wr(x,E,z,A){if(c)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");At(x);let G=(A&&A.flushSync)===!0,de=v||m,pe=gs(L.location,L.matches,p,j.v7_prependBasename,z,j.v7_relativeSplatPath,E,A==null?void 0:A.relative),oe=pn(de,pe,p),ae=qr(oe,de,pe);if(ae.active&&ae.matches&&(oe=ae.matches),!oe){Tt(x,E,Et(404,{pathname:pe}),{flushSync:G});return}let{path:re,submission:le,error:we}=sd(j.v7_normalizeFormMethod,!0,pe,A);if(we){Tt(x,E,we,{flushSync:G});return}let Ee=$l(oe,re),rt=(A&&A.preventScrollReset)===!0;if(le&&Yt(le.formMethod)){tl(x,E,re,Ee,oe,ae.active,G,rt,le);return}fe.set(x,{routeId:E,path:re}),Zl(x,E,re,Ee,oe,ae.active,G,rt,le)}async function tl(x,E,z,A,G,de,pe,oe,ae){Xr(),fe.delete(x);function re(De){if(!De.route.action&&!De.route.lazy){let Er=Et(405,{method:ae.formMethod,pathname:z,routeId:E});return Tt(x,E,Er,{flushSync:pe}),!0}return!1}if(!de&&re(A))return;let le=L.fetchers.get(x);Pt(x,rm(ae,le),{flushSync:pe});let we=new AbortController,Ee=Yn(l.history,z,we.signal,ae);if(de){let De=await Nr(G,new URL(Ee.url).pathname,Ee.signal,x);if(De.type==="aborted")return;if(De.type==="error"){Tt(x,E,De.error,{flushSync:pe});return}else if(De.matches){if(G=De.matches,A=$l(G,z),re(A))return}else{Tt(x,E,Et(404,{pathname:z}),{flushSync:pe});return}}M.set(x,we);let rt=J,_e=(await jr("action",L,Ee,[A],G,x))[A.route.id];if(Ee.signal.aborted){M.get(x)===we&&M.delete(x);return}if(j.v7_fetcherPersist&&ve.has(x)){if(vn(_e)||_t(_e)){Pt(x,$r(void 0));return}}else{if(vn(_e))if(M.delete(x),I>rt){Pt(x,$r(void 0));return}else return O.add(x),Pt(x,Wl(ae)),Jt(Ee,_e,!1,{fetcherSubmission:ae,preventScrollReset:oe});if(_t(_e)){Tt(x,E,_e.error);return}}if(Qr(_e))throw Et(400,{type:"defer-action"});let He=L.navigation.location||L.location,ir=Yn(l.history,He,we.signal),en=v||m,Bt=L.navigation.state!=="idle"?pn(en,L.navigation.location,p):L.matches;Pe(Bt,"Didn't find any matches after fetcher action");let ze=++J;y.set(x,ze);let al=Wl(ae,_e.data);L.fetchers.set(x,al);let[ar,er]=cd(l.history,L,Bt,ae,He,!1,j.v7_skipActionErrorRevalidation,We,Ke,ue,ve,fe,O,en,p,[A.route.id,_e]);er.filter(De=>De.key!==x).forEach(De=>{let Er=De.key,lo=L.fetchers.get(Er),Ni=Wl(void 0,lo?lo.data:void 0);L.fetchers.set(Er,Ni),At(Er),De.controller&&M.set(Er,De.controller)}),ot({fetchers:new Map(L.fetchers)});let sr=()=>er.forEach(De=>At(De.key));we.signal.addEventListener("abort",sr);let{loaderResults:En,fetcherResults:be}=await rl(L,Bt,ar,er,ir);if(we.signal.aborted)return;we.signal.removeEventListener("abort",sr),y.delete(x),M.delete(x),er.forEach(De=>M.delete(De.key));let Ae=ci(En);if(Ae)return Jt(ir,Ae.result,!1,{preventScrollReset:oe});if(Ae=ci(be),Ae)return O.add(Ae.key),Jt(ir,Ae.result,!1,{preventScrollReset:oe});let{loaderData:ut,errors:tn}=pd(L,Bt,En,void 0,er,be,je);if(L.fetchers.has(x)){let De=$r(_e.data);L.fetchers.set(x,De)}Sn(ze),L.navigation.state==="loading"&&ze>I?(Pe(B,"Expected pending action"),Y&&Y.abort(),Ut(L.navigation.location,{matches:Bt,loaderData:ut,errors:tn,fetchers:new Map(L.fetchers)})):(ot({errors:tn,loaderData:gd(L.loaderData,ut,Bt,tn),fetchers:new Map(L.fetchers)}),We=!1)}async function Zl(x,E,z,A,G,de,pe,oe,ae){let re=L.fetchers.get(x);Pt(x,Wl(ae,re?re.data:void 0),{flushSync:pe});let le=new AbortController,we=Yn(l.history,z,le.signal);if(de){let _e=await Nr(G,new URL(we.url).pathname,we.signal,x);if(_e.type==="aborted")return;if(_e.type==="error"){Tt(x,E,_e.error,{flushSync:pe});return}else if(_e.matches)G=_e.matches,A=$l(G,z);else{Tt(x,E,Et(404,{pathname:z}),{flushSync:pe});return}}M.set(x,le);let Ee=J,Fe=(await jr("loader",L,we,[A],G,x))[A.route.id];if(Qr(Fe)&&(Fe=await Ss(Fe,we.signal,!0)||Fe),M.get(x)===le&&M.delete(x),!we.signal.aborted){if(ve.has(x)){Pt(x,$r(void 0));return}if(vn(Fe))if(I>Ee){Pt(x,$r(void 0));return}else{O.add(x),await Jt(we,Fe,!1,{preventScrollReset:oe});return}if(_t(Fe)){Tt(x,E,Fe.error);return}Pe(!Qr(Fe),"Unhandled fetcher deferred data"),Pt(x,$r(Fe.data))}}async function Jt(x,E,z,A){let{submission:G,fetcherSubmission:de,preventScrollReset:pe,replace:oe}=A===void 0?{}:A;E.response.headers.has("X-Remix-Revalidate")&&(We=!0);let ae=E.response.headers.get("Location");Pe(ae,"Expected a Location header on the redirect Response"),ae=hd(ae,new URL(x.url),p);let re=Ql(L.location,ae,{_isRedirect:!0});if(a){let _e=!1;if(E.response.headers.has("X-Remix-Reload-Document"))_e=!0;else if(js.test(ae)){const He=l.history.createURL(ae);_e=He.origin!==s.location.origin||Jn(He.pathname,p)==null}if(_e){oe?s.location.replace(ae):s.location.assign(ae);return}}Y=null;let le=oe===!0||E.response.headers.has("X-Remix-Replace")?tt.Replace:tt.Push,{formMethod:we,formAction:Ee,formEncType:rt}=L.navigation;!G&&!de&&we&&Ee&&rt&&(G=wd(L.navigation));let Fe=G||de;if(Oh.has(E.response.status)&&Fe&&Yt(Fe.formMethod))await Gt(le,re,{submission:Ge({},Fe,{formAction:ae}),preventScrollReset:pe||q,enableViewTransition:z?Se:void 0});else{let _e=ds(re,G);await Gt(le,re,{overrideNavigation:_e,fetcherSubmission:de,preventScrollReset:pe||q,enableViewTransition:z?Se:void 0})}}async function jr(x,E,z,A,G,de){let pe,oe={};try{pe=await Hh(S,x,E,z,A,G,de,h,d)}catch(ae){return A.forEach(re=>{oe[re.route.id]={type:Ue.error,error:ae}}),oe}for(let[ae,re]of Object.entries(pe))if(Jh(re)){let le=re.result;oe[ae]={type:Ue.redirect,response:Kh(le,z,ae,G,p,j.v7_relativeSplatPath)}}else oe[ae]=await Qh(re);return oe}async function rl(x,E,z,A,G){let de=x.matches,pe=jr("loader",x,G,z,E,null),oe=Promise.all(A.map(async le=>{if(le.matches&&le.match&&le.controller){let Ee=(await jr("loader",x,Yn(l.history,le.path,le.controller.signal),[le.match],le.matches,le.key))[le.match.route.id];return{[le.key]:Ee}}else return Promise.resolve({[le.key]:{type:Ue.error,error:Et(404,{pathname:le.path})}})})),ae=await pe,re=(await oe).reduce((le,we)=>Object.assign(le,we),{});return await Promise.all([Zh(E,ae,G.signal,de,x.loaderData),em(E,re,A)]),{loaderResults:ae,fetcherResults:re}}function Xr(){We=!0,Ke.push(...Zt()),fe.forEach((x,E)=>{M.has(E)&&ue.add(E),At(E)})}function Pt(x,E,z){z===void 0&&(z={}),L.fetchers.set(x,E),ot({fetchers:new Map(L.fetchers)},{flushSync:(z&&z.flushSync)===!0})}function Tt(x,E,z,A){A===void 0&&(A={});let G=gn(L.matches,E);Xt(x),ot({errors:{[G.route.id]:z},fetchers:new Map(L.fetchers)},{flushSync:(A&&A.flushSync)===!0})}function Sr(x){return xe.set(x,(xe.get(x)||0)+1),ve.has(x)&&ve.delete(x),L.fetchers.get(x)||Fh}function Xt(x){let E=L.fetchers.get(x);M.has(x)&&!(E&&E.state==="loading"&&y.has(x))&&At(x),fe.delete(x),y.delete(x),O.delete(x),j.v7_fetcherPersist&&ve.delete(x),ue.delete(x),L.fetchers.delete(x)}function ki(x){let E=(xe.get(x)||0)-1;E<=0?(xe.delete(x),ve.add(x),j.v7_fetcherPersist||Xt(x)):xe.set(x,E),ot({fetchers:new Map(L.fetchers)})}function At(x){let E=M.get(x);E&&(E.abort(),M.delete(x))}function nl(x){for(let E of x){let z=Sr(E),A=$r(z.data);L.fetchers.set(E,A)}}function eo(){let x=[],E=!1;for(let z of O){let A=L.fetchers.get(z);Pe(A,"Expected fetcher: "+z),A.state==="loading"&&(O.delete(z),x.push(z),E=!0)}return nl(x),E}function Sn(x){let E=[];for(let[z,A]of y)if(A<x){let G=L.fetchers.get(z);Pe(G,"Expected fetcher: "+z),G.state==="loading"&&(At(z),y.delete(z),E.push(z))}return nl(E),E.length>0}function ll(x,E){let z=L.blockers.get(x)||Vl;return Le.get(x)!==E&&Le.set(x,E),z}function kn(x){L.blockers.delete(x),Le.delete(x)}function kr(x,E){let z=L.blockers.get(x)||Vl;Pe(z.state==="unblocked"&&E.state==="blocked"||z.state==="blocked"&&E.state==="blocked"||z.state==="blocked"&&E.state==="proceeding"||z.state==="blocked"&&E.state==="unblocked"||z.state==="proceeding"&&E.state==="unblocked","Invalid blocker state transition: "+z.state+" -> "+E.state);let A=new Map(L.blockers);A.set(x,E),ot({blockers:A})}function Nn(x){let{currentLocation:E,nextLocation:z,historyAction:A}=x;if(Le.size===0)return;Le.size>1&&Gn(!1,"A router only supports one blocker at a time");let G=Array.from(Le.entries()),[de,pe]=G[G.length-1],oe=L.blockers.get(de);if(!(oe&&oe.state==="proceeding")&&pe({currentLocation:E,nextLocation:z,historyAction:A}))return de}function qt(x){let E=Et(404,{pathname:x}),z=v||m,{matches:A,route:G}=yd(z);return Zt(),{notFoundMatches:A,route:G,error:E}}function Zt(x){let E=[];return je.forEach((z,A)=>{(!x||x(A))&&(z.cancel(),E.push(A),je.delete(A))}),E}function to(x,E,z){if(U=x,_=E,F=z||null,!Z&&L.navigation===cs){Z=!0;let A=il(L.location,L.matches);A!=null&&ot({restoreScrollPosition:A})}return()=>{U=null,_=null,F=null}}function ol(x,E){return F&&F(x,E.map(A=>mh(A,L.loaderData)))||x.key}function ro(x,E){if(U&&_){let z=ol(x,E);U[z]=_()}}function il(x,E){if(U){let z=ol(x,E),A=U[z];if(typeof A=="number")return A}return null}function qr(x,E,z){if(k)if(x){if(Object.keys(x[0].params).length>0)return{active:!0,matches:fi(E,z,p,!0)}}else return{active:!0,matches:fi(E,z,p,!0)||[]};return{active:!1,matches:null}}async function Nr(x,E,z,A){if(!k)return{type:"success",matches:x};let G=x;for(;;){let de=v==null,pe=v||m,oe=h;try{await k({signal:z,path:E,matches:G,fetcherKey:A,patch:(le,we)=>{z.aborted||fd(le,we,pe,oe,d)}})}catch(le){return{type:"error",error:le,partialMatches:G}}finally{de&&!z.aborted&&(m=[...m])}if(z.aborted)return{type:"aborted"};let ae=pn(pe,E,p);if(ae)return{type:"success",matches:ae};let re=fi(pe,E,p,!0);if(!re||G.length===re.length&&G.every((le,we)=>le.route.id===re[we].route.id))return{type:"success",matches:null};G=re}}function no(x){h={},v=hi(x,d,void 0,h)}function Zr(x,E){let z=v==null;fd(x,E,v||m,h,d),z&&(m=[...m],ot({}))}return ge={get basename(){return p},get future(){return j},get state(){return L},get routes(){return m},get window(){return s},initialize:Be,subscribe:xn,enableScrollRestoration:to,navigate:wn,fetch:wr,revalidate:Xl,createHref:x=>l.history.createHref(x),encodeLocation:x=>l.history.encodeLocation(x),getFetcher:Sr,deleteFetcher:ki,dispose:gt,getBlocker:ll,deleteBlocker:kn,patchRoutes:Zr,_internalFetchControllers:M,_internalActiveDeferreds:je,_internalSetRoutes:no},ge}function Ah(l){return l!=null&&("formData"in l&&l.formData!=null||"body"in l&&l.body!==void 0)}function gs(l,s,a,c,d,h,m,v){let p,S;if(m){p=[];for(let j of s)if(p.push(j),j.route.id===m){S=j;break}}else p=s,S=s[s.length-1];let k=xi(d||".",yi(p,h),Jn(l.pathname,a)||l.pathname,v==="path");if(d==null&&(k.search=l.search,k.hash=l.hash),(d==null||d===""||d===".")&&S){let j=ks(k.search);if(S.route.index&&!j)k.search=k.search?k.search.replace(/^\?/,"?index&"):"?index";else if(!S.route.index&&j){let P=new URLSearchParams(k.search),D=P.getAll("index");P.delete("index"),D.filter(F=>F).forEach(F=>P.append("index",F));let U=P.toString();k.search=U?"?"+U:""}}return c&&a!=="/"&&(k.pathname=k.pathname==="/"?a:yr([a,k.pathname])),yn(k)}function sd(l,s,a,c){if(!c||!Ah(c))return{path:a};if(c.formMethod&&!qh(c.formMethod))return{path:a,error:Et(405,{method:c.formMethod})};let d=()=>({path:a,error:Et(400,{type:"invalid-body"})}),h=c.formMethod||"get",m=l?h.toUpperCase():h.toLowerCase(),v=Kd(a);if(c.body!==void 0){if(c.formEncType==="text/plain"){if(!Yt(m))return d();let P=typeof c.body=="string"?c.body:c.body instanceof FormData||c.body instanceof URLSearchParams?Array.from(c.body.entries()).reduce((D,U)=>{let[F,_]=U;return""+D+F+"="+_+`
`},""):String(c.body);return{path:a,submission:{formMethod:m,formAction:v,formEncType:c.formEncType,formData:void 0,json:void 0,text:P}}}else if(c.formEncType==="application/json"){if(!Yt(m))return d();try{let P=typeof c.body=="string"?JSON.parse(c.body):c.body;return{path:a,submission:{formMethod:m,formAction:v,formEncType:c.formEncType,formData:void 0,json:P,text:void 0}}}catch{return d()}}}Pe(typeof FormData=="function","FormData is not available in this environment");let p,S;if(c.formData)p=ys(c.formData),S=c.formData;else if(c.body instanceof FormData)p=ys(c.body),S=c.body;else if(c.body instanceof URLSearchParams)p=c.body,S=md(p);else if(c.body==null)p=new URLSearchParams,S=new FormData;else try{p=new URLSearchParams(c.body),S=md(p)}catch{return d()}let k={formMethod:m,formAction:v,formEncType:c&&c.formEncType||"application/x-www-form-urlencoded",formData:S,json:void 0,text:void 0};if(Yt(k.formMethod))return{path:a,submission:k};let j=Kr(a);return s&&j.search&&ks(j.search)&&p.append("index",""),j.search="?"+p,{path:yn(j),submission:k}}function ud(l,s,a){a===void 0&&(a=!1);let c=l.findIndex(d=>d.route.id===s);return c>=0?l.slice(0,a?c+1:c):l}function cd(l,s,a,c,d,h,m,v,p,S,k,j,P,D,U,F){let _=F?_t(F[1])?F[1].error:F[1].data:void 0,Z=l.createURL(s.location),$=l.createURL(d),K=a;h&&s.errors?K=ud(a,Object.keys(s.errors)[0],!0):F&&_t(F[1])&&(K=ud(a,F[0]));let te=F?F[1].statusCode:void 0,X=m&&te&&te>=400,ge=K.filter((B,q)=>{let{route:Y}=B;if(Y.lazy)return!0;if(Y.loader==null)return!1;if(h)return vs(Y,s.loaderData,s.errors);if(Bh(s.loaderData,s.matches[q],B)||p.some(Ce=>Ce===B.route.id))return!0;let Se=s.matches[q],ke=B;return dd(B,Ge({currentUrl:Z,currentParams:Se.params,nextUrl:$,nextParams:ke.params},c,{actionResult:_,actionStatus:te,defaultShouldRevalidate:X?!1:v||Z.pathname+Z.search===$.pathname+$.search||Z.search!==$.search||$d(Se,ke)}))}),L=[];return j.forEach((B,q)=>{if(h||!a.some(Te=>Te.route.id===B.routeId)||k.has(q))return;let Y=pn(D,B.path,U);if(!Y){L.push({key:q,routeId:B.routeId,path:B.path,matches:null,match:null,controller:null});return}let Se=s.fetchers.get(q),ke=$l(Y,B.path),Ce=!1;P.has(q)?Ce=!1:S.has(q)?(S.delete(q),Ce=!0):Se&&Se.state!=="idle"&&Se.data===void 0?Ce=v:Ce=dd(ke,Ge({currentUrl:Z,currentParams:s.matches[s.matches.length-1].params,nextUrl:$,nextParams:a[a.length-1].params},c,{actionResult:_,actionStatus:te,defaultShouldRevalidate:X?!1:v})),Ce&&L.push({key:q,routeId:B.routeId,path:B.path,matches:Y,match:ke,controller:new AbortController})}),[ge,L]}function vs(l,s,a){if(l.lazy)return!0;if(!l.loader)return!1;let c=s!=null&&s[l.id]!==void 0,d=a!=null&&a[l.id]!==void 0;return!c&&d?!1:typeof l.loader=="function"&&l.loader.hydrate===!0?!0:!c&&!d}function Bh(l,s,a){let c=!s||a.route.id!==s.route.id,d=l[a.route.id]===void 0;return c||d}function $d(l,s){let a=l.route.path;return l.pathname!==s.pathname||a!=null&&a.endsWith("*")&&l.params["*"]!==s.params["*"]}function dd(l,s){if(l.route.shouldRevalidate){let a=l.route.shouldRevalidate(s);if(typeof a=="boolean")return a}return s.defaultShouldRevalidate}function fd(l,s,a,c,d){var h;let m;if(l){let S=c[l];Pe(S,"No route found to patch children into: routeId = "+l),S.children||(S.children=[]),m=S.children}else m=a;let v=s.filter(S=>!m.some(k=>Qd(S,k))),p=hi(v,d,[l||"_","patch",String(((h=m)==null?void 0:h.length)||"0")],c);m.push(...p)}function Qd(l,s){return"id"in l&&"id"in s&&l.id===s.id?!0:l.index===s.index&&l.path===s.path&&l.caseSensitive===s.caseSensitive?(!l.children||l.children.length===0)&&(!s.children||s.children.length===0)?!0:l.children.every((a,c)=>{var d;return(d=s.children)==null?void 0:d.some(h=>Qd(a,h))}):!1}async function Vh(l,s,a){if(!l.lazy)return;let c=await l.lazy();if(!l.lazy)return;let d=a[l.id];Pe(d,"No route found in manifest");let h={};for(let m in c){let p=d[m]!==void 0&&m!=="hasErrorBoundary";Gn(!p,'Route "'+d.id+'" has a static property "'+m+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+m+'" will be ignored.')),!p&&!fh.has(m)&&(h[m]=c[m])}Object.assign(d,h),Object.assign(d,Ge({},s(d),{lazy:void 0}))}async function Wh(l){let{matches:s}=l,a=s.filter(d=>d.shouldLoad);return(await Promise.all(a.map(d=>d.resolve()))).reduce((d,h,m)=>Object.assign(d,{[a[m].route.id]:h}),{})}async function Hh(l,s,a,c,d,h,m,v,p,S){let k=h.map(D=>D.route.lazy?Vh(D.route,p,v):void 0),j=h.map((D,U)=>{let F=k[U],_=d.some($=>$.route.id===D.route.id);return Ge({},D,{shouldLoad:_,resolve:async $=>($&&c.method==="GET"&&(D.route.lazy||D.route.loader)&&(_=!0),_?$h(s,c,D,F,$,S):Promise.resolve({type:Ue.data,result:void 0}))})}),P=await l({matches:j,request:c,params:h[0].params,fetcherKey:m,context:S});try{await Promise.all(k)}catch{}return P}async function $h(l,s,a,c,d,h){let m,v,p=S=>{let k,j=new Promise((U,F)=>k=F);v=()=>k(),s.signal.addEventListener("abort",v);let P=U=>typeof S!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+l+'" [routeId: '+a.route.id+"]"))):S({request:s,params:a.params,context:h},...U!==void 0?[U]:[]),D=(async()=>{try{return{type:"data",result:await(d?d(F=>P(F)):P())}}catch(U){return{type:"error",result:U}}})();return Promise.race([D,j])};try{let S=a.route[l];if(c)if(S){let k,[j]=await Promise.all([p(S).catch(P=>{k=P}),c]);if(k!==void 0)throw k;m=j}else if(await c,S=a.route[l],S)m=await p(S);else if(l==="action"){let k=new URL(s.url),j=k.pathname+k.search;throw Et(405,{method:s.method,pathname:j,routeId:a.route.id})}else return{type:Ue.data,result:void 0};else if(S)m=await p(S);else{let k=new URL(s.url),j=k.pathname+k.search;throw Et(404,{pathname:j})}Pe(m.result!==void 0,"You defined "+(l==="action"?"an action":"a loader")+" for route "+('"'+a.route.id+"\" but didn't return anything from your `"+l+"` ")+"function. Please return a value or `null`.")}catch(S){return{type:Ue.error,result:S}}finally{v&&s.signal.removeEventListener("abort",v)}return m}async function Qh(l){let{result:s,type:a}=l;if(Yd(s)){let j;try{let P=s.headers.get("Content-Type");P&&/\bapplication\/json\b/.test(P)?s.body==null?j=null:j=await s.json():j=await s.text()}catch(P){return{type:Ue.error,error:P}}return a===Ue.error?{type:Ue.error,error:new mi(s.status,s.statusText,j),statusCode:s.status,headers:s.headers}:{type:Ue.data,data:j,statusCode:s.status,headers:s.headers}}if(a===Ue.error){if(xd(s)){var c,d;if(s.data instanceof Error){var h,m;return{type:Ue.error,error:s.data,statusCode:(h=s.init)==null?void 0:h.status,headers:(m=s.init)!=null&&m.headers?new Headers(s.init.headers):void 0}}return{type:Ue.error,error:new mi(((c=s.init)==null?void 0:c.status)||500,void 0,s.data),statusCode:Kl(s)?s.status:void 0,headers:(d=s.init)!=null&&d.headers?new Headers(s.init.headers):void 0}}return{type:Ue.error,error:s,statusCode:Kl(s)?s.status:void 0}}if(Xh(s)){var v,p;return{type:Ue.deferred,deferredData:s,statusCode:(v=s.init)==null?void 0:v.status,headers:((p=s.init)==null?void 0:p.headers)&&new Headers(s.init.headers)}}if(xd(s)){var S,k;return{type:Ue.data,data:s.data,statusCode:(S=s.init)==null?void 0:S.status,headers:(k=s.init)!=null&&k.headers?new Headers(s.init.headers):void 0}}return{type:Ue.data,data:s}}function Kh(l,s,a,c,d,h){let m=l.headers.get("Location");if(Pe(m,"Redirects returned/thrown from loaders/actions must have a Location header"),!js.test(m)){let v=c.slice(0,c.findIndex(p=>p.route.id===a)+1);m=gs(new URL(s.url),v,d,!0,m,h),l.headers.set("Location",m)}return l}function hd(l,s,a){if(js.test(l)){let c=l,d=c.startsWith("//")?new URL(s.protocol+c):new URL(c),h=Jn(d.pathname,a)!=null;if(d.origin===s.origin&&h)return d.pathname+d.search+d.hash}return l}function Yn(l,s,a,c){let d=l.createURL(Kd(s)).toString(),h={signal:a};if(c&&Yt(c.formMethod)){let{formMethod:m,formEncType:v}=c;h.method=m.toUpperCase(),v==="application/json"?(h.headers=new Headers({"Content-Type":v}),h.body=JSON.stringify(c.json)):v==="text/plain"?h.body=c.text:v==="application/x-www-form-urlencoded"&&c.formData?h.body=ys(c.formData):h.body=c.formData}return new Request(d,h)}function ys(l){let s=new URLSearchParams;for(let[a,c]of l.entries())s.append(a,typeof c=="string"?c:c.name);return s}function md(l){let s=new FormData;for(let[a,c]of l.entries())s.append(a,c);return s}function Yh(l,s,a,c,d){let h={},m=null,v,p=!1,S={},k=a&&_t(a[1])?a[1].error:void 0;return l.forEach(j=>{if(!(j.route.id in s))return;let P=j.route.id,D=s[P];if(Pe(!vn(D),"Cannot handle redirect results in processLoaderData"),_t(D)){let U=D.error;k!==void 0&&(U=k,k=void 0),m=m||{};{let F=gn(l,P);m[F.route.id]==null&&(m[F.route.id]=U)}h[P]=void 0,p||(p=!0,v=Kl(D.error)?D.error.status:500),D.headers&&(S[P]=D.headers)}else Qr(D)?(c.set(P,D.deferredData),h[P]=D.deferredData.data,D.statusCode!=null&&D.statusCode!==200&&!p&&(v=D.statusCode),D.headers&&(S[P]=D.headers)):(h[P]=D.data,D.statusCode&&D.statusCode!==200&&!p&&(v=D.statusCode),D.headers&&(S[P]=D.headers))}),k!==void 0&&a&&(m={[a[0]]:k},h[a[0]]=void 0),{loaderData:h,errors:m,statusCode:v||200,loaderHeaders:S}}function pd(l,s,a,c,d,h,m){let{loaderData:v,errors:p}=Yh(s,a,c,m);return d.forEach(S=>{let{key:k,match:j,controller:P}=S,D=h[k];if(Pe(D,"Did not find corresponding fetcher result"),!(P&&P.signal.aborted))if(_t(D)){let U=gn(l.matches,j==null?void 0:j.route.id);p&&p[U.route.id]||(p=Ge({},p,{[U.route.id]:D.error})),l.fetchers.delete(k)}else if(vn(D))Pe(!1,"Unhandled fetcher revalidation redirect");else if(Qr(D))Pe(!1,"Unhandled fetcher deferred data");else{let U=$r(D.data);l.fetchers.set(k,U)}}),{loaderData:v,errors:p}}function gd(l,s,a,c){let d=Ge({},s);for(let h of a){let m=h.route.id;if(s.hasOwnProperty(m)?s[m]!==void 0&&(d[m]=s[m]):l[m]!==void 0&&h.route.loader&&(d[m]=l[m]),c&&c.hasOwnProperty(m))break}return d}function vd(l){return l?_t(l[1])?{actionData:{}}:{actionData:{[l[0]]:l[1].data}}:{}}function gn(l,s){return(s?l.slice(0,l.findIndex(c=>c.route.id===s)+1):[...l]).reverse().find(c=>c.route.hasErrorBoundary===!0)||l[0]}function yd(l){let s=l.length===1?l[0]:l.find(a=>a.index||!a.path||a.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:s}],route:s}}function Et(l,s){let{pathname:a,routeId:c,method:d,type:h,message:m}=s===void 0?{}:s,v="Unknown Server Error",p="Unknown @remix-run/router error";return l===400?(v="Bad Request",d&&a&&c?p="You made a "+d+' request to "'+a+'" but '+('did not provide a `loader` for route "'+c+'", ')+"so there is no way to handle the request.":h==="defer-action"?p="defer() is not supported in actions":h==="invalid-body"&&(p="Unable to encode submission body")):l===403?(v="Forbidden",p='Route "'+c+'" does not match URL "'+a+'"'):l===404?(v="Not Found",p='No route matches URL "'+a+'"'):l===405&&(v="Method Not Allowed",d&&a&&c?p="You made a "+d.toUpperCase()+' request to "'+a+'" but '+('did not provide an `action` for route "'+c+'", ')+"so there is no way to handle the request.":d&&(p='Invalid request method "'+d.toUpperCase()+'"')),new mi(l||500,v,new Error(p),!0)}function ci(l){let s=Object.entries(l);for(let a=s.length-1;a>=0;a--){let[c,d]=s[a];if(vn(d))return{key:c,result:d}}}function Kd(l){let s=typeof l=="string"?Kr(l):l;return yn(Ge({},s,{hash:""}))}function Gh(l,s){return l.pathname!==s.pathname||l.search!==s.search?!1:l.hash===""?s.hash!=="":l.hash===s.hash?!0:s.hash!==""}function Jh(l){return Yd(l.result)&&Dh.has(l.result.status)}function Qr(l){return l.type===Ue.deferred}function _t(l){return l.type===Ue.error}function vn(l){return(l&&l.type)===Ue.redirect}function xd(l){return typeof l=="object"&&l!=null&&"type"in l&&"data"in l&&"init"in l&&l.type==="DataWithResponseInit"}function Xh(l){let s=l;return s&&typeof s=="object"&&typeof s.data=="object"&&typeof s.subscribe=="function"&&typeof s.cancel=="function"&&typeof s.resolveData=="function"}function Yd(l){return l!=null&&typeof l.status=="number"&&typeof l.statusText=="string"&&typeof l.headers=="object"&&typeof l.body<"u"}function qh(l){return zh.has(l.toLowerCase())}function Yt(l){return Th.has(l.toLowerCase())}async function Zh(l,s,a,c,d){let h=Object.entries(s);for(let m=0;m<h.length;m++){let[v,p]=h[m],S=l.find(P=>(P==null?void 0:P.route.id)===v);if(!S)continue;let k=c.find(P=>P.route.id===S.route.id),j=k!=null&&!$d(k,S)&&(d&&d[S.route.id])!==void 0;Qr(p)&&j&&await Ss(p,a,!1).then(P=>{P&&(s[v]=P)})}}async function em(l,s,a){for(let c=0;c<a.length;c++){let{key:d,routeId:h,controller:m}=a[c],v=s[d];l.find(S=>(S==null?void 0:S.route.id)===h)&&Qr(v)&&(Pe(m,"Expected an AbortController for revalidating fetcher deferred result"),await Ss(v,m.signal,!0).then(S=>{S&&(s[d]=S)}))}}async function Ss(l,s,a){if(a===void 0&&(a=!1),!await l.deferredData.resolveData(s)){if(a)try{return{type:Ue.data,data:l.deferredData.unwrappedData}}catch(d){return{type:Ue.error,error:d}}return{type:Ue.data,data:l.deferredData.data}}}function ks(l){return new URLSearchParams(l).getAll("index").some(s=>s==="")}function $l(l,s){let a=typeof s=="string"?Kr(s).search:s.search;if(l[l.length-1].route.index&&ks(a||""))return l[l.length-1];let c=Vd(l);return c[c.length-1]}function wd(l){let{formMethod:s,formAction:a,formEncType:c,text:d,formData:h,json:m}=l;if(!(!s||!a||!c)){if(d!=null)return{formMethod:s,formAction:a,formEncType:c,formData:void 0,json:void 0,text:d};if(h!=null)return{formMethod:s,formAction:a,formEncType:c,formData:h,json:void 0,text:void 0};if(m!==void 0)return{formMethod:s,formAction:a,formEncType:c,formData:void 0,json:m,text:void 0}}}function ds(l,s){return s?{state:"loading",location:l,formMethod:s.formMethod,formAction:s.formAction,formEncType:s.formEncType,formData:s.formData,json:s.json,text:s.text}:{state:"loading",location:l,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function tm(l,s){return{state:"submitting",location:l,formMethod:s.formMethod,formAction:s.formAction,formEncType:s.formEncType,formData:s.formData,json:s.json,text:s.text}}function Wl(l,s){return l?{state:"loading",formMethod:l.formMethod,formAction:l.formAction,formEncType:l.formEncType,formData:l.formData,json:l.json,text:l.text,data:s}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:s}}function rm(l,s){return{state:"submitting",formMethod:l.formMethod,formAction:l.formAction,formEncType:l.formEncType,formData:l.formData,json:l.json,text:l.text,data:s?s.data:void 0}}function $r(l){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:l}}function nm(l,s){try{let a=l.sessionStorage.getItem(Hd);if(a){let c=JSON.parse(a);for(let[d,h]of Object.entries(c||{}))h&&Array.isArray(h)&&s.set(d,new Set(h||[]))}}catch{}}function lm(l,s){if(s.size>0){let a={};for(let[c,d]of s)a[c]=[...d];try{l.sessionStorage.setItem(Hd,JSON.stringify(a))}catch(c){Gn(!1,"Failed to save applied view transitions in sessionStorage ("+c+").")}}}/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pi(){return pi=Object.assign?Object.assign.bind():function(l){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(l[c]=a[c])}return l},pi.apply(this,arguments)}const wi=C.createContext(null),Gd=C.createContext(null),Yr=C.createContext(null),Ns=C.createContext(null),xr=C.createContext({outlet:null,matches:[],isDataRoute:!1}),Jd=C.createContext(null);function om(l,s){let{relative:a}=s===void 0?{}:s;Xn()||Pe(!1);let{basename:c,navigator:d}=C.useContext(Yr),{hash:h,pathname:m,search:v}=qd(l,{relative:a}),p=m;return c!=="/"&&(p=m==="/"?c:yr([c,m])),d.createHref({pathname:p,search:v,hash:h})}function Xn(){return C.useContext(Ns)!=null}function qn(){return Xn()||Pe(!1),C.useContext(Ns).location}function Xd(l){C.useContext(Yr).static||C.useLayoutEffect(l)}function Gr(){let{isDataRoute:l}=C.useContext(xr);return l?xm():im()}function im(){Xn()||Pe(!1);let l=C.useContext(wi),{basename:s,future:a,navigator:c}=C.useContext(Yr),{matches:d}=C.useContext(xr),{pathname:h}=qn(),m=JSON.stringify(yi(d,a.v7_relativeSplatPath)),v=C.useRef(!1);return Xd(()=>{v.current=!0}),C.useCallback(function(S,k){if(k===void 0&&(k={}),!v.current)return;if(typeof S=="number"){c.go(S);return}let j=xi(S,JSON.parse(m),h,k.relative==="path");l==null&&s!=="/"&&(j.pathname=j.pathname==="/"?s:yr([s,j.pathname])),(k.replace?c.replace:c.push)(j,k.state,k)},[s,c,m,h,l])}const am=C.createContext(null);function sm(l){let s=C.useContext(xr).outlet;return s&&C.createElement(am.Provider,{value:l},s)}function qd(l,s){let{relative:a}=s===void 0?{}:s,{future:c}=C.useContext(Yr),{matches:d}=C.useContext(xr),{pathname:h}=qn(),m=JSON.stringify(yi(d,c.v7_relativeSplatPath));return C.useMemo(()=>xi(l,JSON.parse(m),h,a==="path"),[l,m,h,a])}function um(l,s,a,c){Xn()||Pe(!1);let{navigator:d,static:h}=C.useContext(Yr),{matches:m}=C.useContext(xr),v=m[m.length-1],p=v?v.params:{};v&&v.pathname;let S=v?v.pathnameBase:"/";v&&v.route;let k=qn(),j;j=k;let P=j.pathname||"/",D=P;if(S!=="/"){let _=S.replace(/^\//,"").split("/");D="/"+P.replace(/^\//,"").split("/").slice(_.length).join("/")}let U=!h&&a&&a.matches&&a.matches.length>0?a.matches:pn(l,{pathname:D});return mm(U&&U.map(_=>Object.assign({},_,{params:Object.assign({},p,_.params),pathname:yr([S,d.encodeLocation?d.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?S:yr([S,d.encodeLocation?d.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),m,a,c)}function cm(){let l=ym(),s=Kl(l)?l.status+" "+l.statusText:l instanceof Error?l.message:JSON.stringify(l),a=l instanceof Error?l.stack:null,d={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return C.createElement(C.Fragment,null,C.createElement("h2",null,"Unexpected Application Error!"),C.createElement("h3",{style:{fontStyle:"italic"}},s),a?C.createElement("pre",{style:d},a):null,null)}const dm=C.createElement(cm,null);class fm extends C.Component{constructor(s){super(s),this.state={location:s.location,revalidation:s.revalidation,error:s.error}}static getDerivedStateFromError(s){return{error:s}}static getDerivedStateFromProps(s,a){return a.location!==s.location||a.revalidation!=="idle"&&s.revalidation==="idle"?{error:s.error,location:s.location,revalidation:s.revalidation}:{error:s.error!==void 0?s.error:a.error,location:a.location,revalidation:s.revalidation||a.revalidation}}componentDidCatch(s,a){console.error("React Router caught the following error during render",s,a)}render(){return this.state.error!==void 0?C.createElement(xr.Provider,{value:this.props.routeContext},C.createElement(Jd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function hm(l){let{routeContext:s,match:a,children:c}=l,d=C.useContext(wi);return d&&d.static&&d.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(d.staticContext._deepestRenderedBoundaryId=a.route.id),C.createElement(xr.Provider,{value:s},c)}function mm(l,s,a,c){var d;if(s===void 0&&(s=[]),a===void 0&&(a=null),c===void 0&&(c=null),l==null){var h;if(!a)return null;if(a.errors)l=a.matches;else if((h=c)!=null&&h.v7_partialHydration&&s.length===0&&!a.initialized&&a.matches.length>0)l=a.matches;else return null}let m=l,v=(d=a)==null?void 0:d.errors;if(v!=null){let k=m.findIndex(j=>j.route.id&&(v==null?void 0:v[j.route.id])!==void 0);k>=0||Pe(!1),m=m.slice(0,Math.min(m.length,k+1))}let p=!1,S=-1;if(a&&c&&c.v7_partialHydration)for(let k=0;k<m.length;k++){let j=m[k];if((j.route.HydrateFallback||j.route.hydrateFallbackElement)&&(S=k),j.route.id){let{loaderData:P,errors:D}=a,U=j.route.loader&&P[j.route.id]===void 0&&(!D||D[j.route.id]===void 0);if(j.route.lazy||U){p=!0,S>=0?m=m.slice(0,S+1):m=[m[0]];break}}}return m.reduceRight((k,j,P)=>{let D,U=!1,F=null,_=null;a&&(D=v&&j.route.id?v[j.route.id]:void 0,F=j.route.errorElement||dm,p&&(S<0&&P===0?(wm("route-fallback"),U=!0,_=null):S===P&&(U=!0,_=j.route.hydrateFallbackElement||null)));let Z=s.concat(m.slice(0,P+1)),$=()=>{let K;return D?K=F:U?K=_:j.route.Component?K=C.createElement(j.route.Component,null):j.route.element?K=j.route.element:K=k,C.createElement(hm,{match:j,routeContext:{outlet:k,matches:Z,isDataRoute:a!=null},children:K})};return a&&(j.route.ErrorBoundary||j.route.errorElement||P===0)?C.createElement(fm,{location:a.location,revalidation:a.revalidation,component:F,error:D,children:$(),routeContext:{outlet:null,matches:Z,isDataRoute:!0}}):$()},null)}var Zd=function(l){return l.UseBlocker="useBlocker",l.UseRevalidator="useRevalidator",l.UseNavigateStable="useNavigate",l}(Zd||{}),ef=function(l){return l.UseBlocker="useBlocker",l.UseLoaderData="useLoaderData",l.UseActionData="useActionData",l.UseRouteError="useRouteError",l.UseNavigation="useNavigation",l.UseRouteLoaderData="useRouteLoaderData",l.UseMatches="useMatches",l.UseRevalidator="useRevalidator",l.UseNavigateStable="useNavigate",l.UseRouteId="useRouteId",l}(ef||{});function pm(l){let s=C.useContext(wi);return s||Pe(!1),s}function gm(l){let s=C.useContext(Gd);return s||Pe(!1),s}function vm(l){let s=C.useContext(xr);return s||Pe(!1),s}function tf(l){let s=vm(),a=s.matches[s.matches.length-1];return a.route.id||Pe(!1),a.route.id}function ym(){var l;let s=C.useContext(Jd),a=gm(ef.UseRouteError),c=tf();return s!==void 0?s:(l=a.errors)==null?void 0:l[c]}function xm(){let{router:l}=pm(Zd.UseNavigateStable),s=tf(),a=C.useRef(!1);return Xd(()=>{a.current=!0}),C.useCallback(function(d,h){h===void 0&&(h={}),a.current&&(typeof d=="number"?l.navigate(d):l.navigate(d,pi({fromRouteId:s},h)))},[l,s])}const jd={};function wm(l,s,a){jd[l]||(jd[l]=!0)}function jm(l,s){l==null||l.v7_startTransition,(l==null?void 0:l.v7_relativeSplatPath)===void 0&&(!s||s.v7_relativeSplatPath),s&&(s.v7_fetcherPersist,s.v7_normalizeFormMethod,s.v7_partialHydration,s.v7_skipActionErrorRevalidation)}function ji(l){let{to:s,replace:a,state:c,relative:d}=l;Xn()||Pe(!1);let{future:h,static:m}=C.useContext(Yr),{matches:v}=C.useContext(xr),{pathname:p}=qn(),S=Gr(),k=xi(s,yi(v,h.v7_relativeSplatPath),p,d==="path"),j=JSON.stringify(k);return C.useEffect(()=>S(JSON.parse(j),{replace:a,state:c,relative:d}),[S,j,d,a,c]),null}function rf(l){return sm(l.context)}function Sm(l){let{basename:s="/",children:a=null,location:c,navigationType:d=tt.Pop,navigator:h,static:m=!1,future:v}=l;Xn()&&Pe(!1);let p=s.replace(/^\/*/,"/"),S=C.useMemo(()=>({basename:p,navigator:h,static:m,future:pi({v7_relativeSplatPath:!1},v)}),[p,v,h,m]);typeof c=="string"&&(c=Kr(c));let{pathname:k="/",search:j="",hash:P="",state:D=null,key:U="default"}=c,F=C.useMemo(()=>{let _=Jn(k,p);return _==null?null:{location:{pathname:_,search:j,hash:P,state:D,key:U},navigationType:d}},[p,k,j,P,D,U,d]);return F==null?null:C.createElement(Yr.Provider,{value:S},C.createElement(Ns.Provider,{children:a,value:F}))}new Promise(()=>{});function km(l){let s={hasErrorBoundary:l.ErrorBoundary!=null||l.errorElement!=null};return l.Component&&Object.assign(s,{element:C.createElement(l.Component),Component:void 0}),l.HydrateFallback&&Object.assign(s,{hydrateFallbackElement:C.createElement(l.HydrateFallback),HydrateFallback:void 0}),l.ErrorBoundary&&Object.assign(s,{errorElement:C.createElement(l.ErrorBoundary),ErrorBoundary:void 0}),s}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Yl(){return Yl=Object.assign?Object.assign.bind():function(l){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(l[c]=a[c])}return l},Yl.apply(this,arguments)}function Nm(l,s){if(l==null)return{};var a={},c=Object.keys(l),d,h;for(h=0;h<c.length;h++)d=c[h],!(s.indexOf(d)>=0)&&(a[d]=l[d]);return a}function Em(l){return!!(l.metaKey||l.altKey||l.ctrlKey||l.shiftKey)}function Pm(l,s){return l.button===0&&(!s||s==="_self")&&!Em(l)}function xs(l){return l===void 0&&(l=""),new URLSearchParams(typeof l=="string"||Array.isArray(l)||l instanceof URLSearchParams?l:Object.keys(l).reduce((s,a)=>{let c=l[a];return s.concat(Array.isArray(c)?c.map(d=>[a,d]):[[a,c]])},[]))}function Cm(l,s){let a=xs(l);return s&&s.forEach((c,d)=>{a.has(d)||s.getAll(d).forEach(h=>{a.append(d,h)})}),a}const bm=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Lm="6";try{window.__reactRouterVersion=Lm}catch{}function Rm(l,s){return Uh({basename:void 0,future:Yl({},void 0,{v7_prependBasename:!0}),history:uh({window:void 0}),hydrationData:_m(),routes:l,mapRouteProperties:km,dataStrategy:void 0,patchRoutesOnNavigation:void 0,window:void 0}).initialize()}function _m(){var l;let s=(l=window)==null?void 0:l.__staticRouterHydrationData;return s&&s.errors&&(s=Yl({},s,{errors:Tm(s.errors)})),s}function Tm(l){if(!l)return null;let s=Object.entries(l),a={};for(let[c,d]of s)if(d&&d.__type==="RouteErrorResponse")a[c]=new mi(d.status,d.statusText,d.data,d.internal===!0);else if(d&&d.__type==="Error"){if(d.__subType){let h=window[d.__subType];if(typeof h=="function")try{let m=new h(d.message);m.stack="",a[c]=m}catch{}}if(a[c]==null){let h=new Error(d.message);h.stack="",a[c]=h}}else a[c]=d;return a}const Mm=C.createContext({isTransitioning:!1}),zm=C.createContext(new Map),Dm="startTransition",Sd=th[Dm],Om="flushSync",kd=sh[Om];function Fm(l){Sd?Sd(l):l()}function Hl(l){kd?kd(l):l()}class Im{constructor(){this.status="pending",this.promise=new Promise((s,a)=>{this.resolve=c=>{this.status==="pending"&&(this.status="resolved",s(c))},this.reject=c=>{this.status==="pending"&&(this.status="rejected",a(c))}})}}function Um(l){let{fallbackElement:s,router:a,future:c}=l,[d,h]=C.useState(a.state),[m,v]=C.useState(),[p,S]=C.useState({isTransitioning:!1}),[k,j]=C.useState(),[P,D]=C.useState(),[U,F]=C.useState(),_=C.useRef(new Map),{v7_startTransition:Z}=c||{},$=C.useCallback(B=>{Z?Fm(B):B()},[Z]),K=C.useCallback((B,q)=>{let{deletedFetchers:Y,flushSync:Se,viewTransitionOpts:ke}=q;B.fetchers.forEach((Te,We)=>{Te.data!==void 0&&_.current.set(We,Te.data)}),Y.forEach(Te=>_.current.delete(Te));let Ce=a.window==null||a.window.document==null||typeof a.window.document.startViewTransition!="function";if(!ke||Ce){Se?Hl(()=>h(B)):$(()=>h(B));return}if(Se){Hl(()=>{P&&(k&&k.resolve(),P.skipTransition()),S({isTransitioning:!0,flushSync:!0,currentLocation:ke.currentLocation,nextLocation:ke.nextLocation})});let Te=a.window.document.startViewTransition(()=>{Hl(()=>h(B))});Te.finished.finally(()=>{Hl(()=>{j(void 0),D(void 0),v(void 0),S({isTransitioning:!1})})}),Hl(()=>D(Te));return}P?(k&&k.resolve(),P.skipTransition(),F({state:B,currentLocation:ke.currentLocation,nextLocation:ke.nextLocation})):(v(B),S({isTransitioning:!0,flushSync:!1,currentLocation:ke.currentLocation,nextLocation:ke.nextLocation}))},[a.window,P,k,_,$]);C.useLayoutEffect(()=>a.subscribe(K),[a,K]),C.useEffect(()=>{p.isTransitioning&&!p.flushSync&&j(new Im)},[p]),C.useEffect(()=>{if(k&&m&&a.window){let B=m,q=k.promise,Y=a.window.document.startViewTransition(async()=>{$(()=>h(B)),await q});Y.finished.finally(()=>{j(void 0),D(void 0),v(void 0),S({isTransitioning:!1})}),D(Y)}},[$,m,k,a.window]),C.useEffect(()=>{k&&m&&d.location.key===m.location.key&&k.resolve()},[k,P,d.location,m]),C.useEffect(()=>{!p.isTransitioning&&U&&(v(U.state),S({isTransitioning:!0,flushSync:!1,currentLocation:U.currentLocation,nextLocation:U.nextLocation}),F(void 0))},[p.isTransitioning,U]),C.useEffect(()=>{},[]);let te=C.useMemo(()=>({createHref:a.createHref,encodeLocation:a.encodeLocation,go:B=>a.navigate(B),push:(B,q,Y)=>a.navigate(B,{state:q,preventScrollReset:Y==null?void 0:Y.preventScrollReset}),replace:(B,q,Y)=>a.navigate(B,{replace:!0,state:q,preventScrollReset:Y==null?void 0:Y.preventScrollReset})}),[a]),X=a.basename||"/",ge=C.useMemo(()=>({router:a,navigator:te,static:!1,basename:X}),[a,te,X]),L=C.useMemo(()=>({v7_relativeSplatPath:a.future.v7_relativeSplatPath}),[a.future.v7_relativeSplatPath]);return C.useEffect(()=>jm(c,a.future),[c,a.future]),C.createElement(C.Fragment,null,C.createElement(wi.Provider,{value:ge},C.createElement(Gd.Provider,{value:d},C.createElement(zm.Provider,{value:_.current},C.createElement(Mm.Provider,{value:p},C.createElement(Sm,{basename:X,location:d.location,navigationType:d.historyAction,navigator:te,future:L},d.initialized||a.future.v7_partialHydration?C.createElement(Am,{routes:a.routes,future:a.future,state:d}):s))))),null)}const Am=C.memo(Bm);function Bm(l){let{routes:s,future:a,state:c}=l;return um(s,void 0,c,a)}const Vm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Wm=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,di=C.forwardRef(function(s,a){let{onClick:c,relative:d,reloadDocument:h,replace:m,state:v,target:p,to:S,preventScrollReset:k,viewTransition:j}=s,P=Nm(s,bm),{basename:D}=C.useContext(Yr),U,F=!1;if(typeof S=="string"&&Wm.test(S)&&(U=S,Vm))try{let K=new URL(window.location.href),te=S.startsWith("//")?new URL(K.protocol+S):new URL(S),X=Jn(te.pathname,D);te.origin===K.origin&&X!=null?S=X+te.search+te.hash:F=!0}catch{}let _=om(S,{relative:d}),Z=Hm(S,{replace:m,state:v,target:p,preventScrollReset:k,relative:d,viewTransition:j});function $(K){c&&c(K),K.defaultPrevented||Z(K)}return C.createElement("a",Yl({},P,{href:U||_,onClick:F||h?c:$,ref:a,target:p}))});var Nd;(function(l){l.UseScrollRestoration="useScrollRestoration",l.UseSubmit="useSubmit",l.UseSubmitFetcher="useSubmitFetcher",l.UseFetcher="useFetcher",l.useViewTransitionState="useViewTransitionState"})(Nd||(Nd={}));var Ed;(function(l){l.UseFetcher="useFetcher",l.UseFetchers="useFetchers",l.UseScrollRestoration="useScrollRestoration"})(Ed||(Ed={}));function Hm(l,s){let{target:a,replace:c,state:d,preventScrollReset:h,relative:m,viewTransition:v}=s===void 0?{}:s,p=Gr(),S=qn(),k=qd(l,{relative:m});return C.useCallback(j=>{if(Pm(j,a)){j.preventDefault();let P=c!==void 0?c:yn(S)===yn(k);p(l,{replace:P,state:d,preventScrollReset:h,relative:m,viewTransition:v})}},[S,p,k,c,d,a,l,h,m,v])}function nf(l){let s=C.useRef(xs(l)),a=C.useRef(!1),c=qn(),d=C.useMemo(()=>Cm(c.search,a.current?null:s.current),[c.search]),h=Gr(),m=C.useCallback((v,p)=>{const S=xs(typeof v=="function"?v(d):v);a.current=!0,h("?"+S,p)},[h,d]);return[d,m]}const $m=({count:l=50,color:s="rgba(74, 222, 128, 0.8)",glowColor:a="rgba(74, 222, 128, 0.5)",minSize:c=1,maxSize:d=3,minDuration:h=5,maxDuration:m=10,topOffset:v=0,concentrateTop:p=!0,mouseRepelRadius:S=100,mouseRepelStrength:k=.5})=>{const[j,P]=C.useState([]),[D,U]=C.useState({x:-1e3,y:-1e3}),F=C.useRef(null);return C.useEffect(()=>{const _=Z=>{if(F.current){const $=F.current.getBoundingClientRect();U({x:Z.clientX-$.left,y:Z.clientY-$.top})}};return window.addEventListener("mousemove",_),()=>{window.removeEventListener("mousemove",_)}},[]),C.useEffect(()=>{P((console.log(`Generating ${l} fireflies`),Array.from({length:l},(Z,$)=>{const K=p?Math.pow(Math.random(),2)*(100-v)+v:Math.random()*(100-v)+v,te=Math.random()*100,X=Math.random()*(d-c)+c,ge=Math.random()*(m-h)+h,L=Math.random()*15;return{id:$,leftPercent:te,topPercent:K,leftPx:0,topPx:0,originalLeftPx:0,originalTopPx:0,size:X,animationDuration:`${ge}s`,animationDelay:`${L}s`,color:s,glowColor:a,repelling:!1}})))},[l,s,a,c,d,h,m,v,p]),C.useEffect(()=>{if(F.current&&j.length>0){const _=F.current.offsetWidth,Z=F.current.offsetHeight,$=j.map(K=>{if(K.leftPx!==0&&K.topPx!==0)return K;const te=K.leftPercent/100*_,X=K.topPercent/100*Z;return{...K,leftPx:te,topPx:X,originalLeftPx:te,originalTopPx:X}});P($)}},[j.length]),C.useEffect(()=>{if(!F.current||j.length===0)return;let _;const Z=()=>{const $=F.current.offsetWidth,K=F.current.offsetHeight,te=[...j].map(X=>{if(X.leftPx===void 0||X.leftPx===0)return X;const ge=X.leftPx-D.x,L=X.topPx-D.y,B=Math.sqrt(ge*ge+L*L);if(B<S){const q=(1-B/S)*k;let Y=X.leftPx+ge/B*q*50,Se=X.topPx+L/B*q*50;Y=Math.max(0,Math.min($,Y)),Se=Math.max(0,Math.min(K,Se));const ke=Y/$*100,Ce=Se/K*100;return{...X,leftPx:Y,topPx:Se,leftPercent:ke,topPercent:Ce,repelling:!0}}else if(X.repelling){const Y=X.leftPx+(X.originalLeftPx-X.leftPx)*.1,Se=X.topPx+(X.originalTopPx-X.topPx)*.1,ke=Y/$*100,Ce=Se/K*100,Te=Math.abs(Y-X.originalLeftPx)<1&&Math.abs(Se-X.originalTopPx)<1;return{...X,leftPx:Y,topPx:Se,leftPercent:ke,topPercent:Ce,repelling:!Te}}return X});P(te),_=requestAnimationFrame(Z)};return _=requestAnimationFrame(Z),()=>{cancelAnimationFrame(_)}},[D.x,D.y,S,k]),i.jsx("div",{className:"firefly-container",ref:F,children:j.map(_=>i.jsx("div",{className:`firefly ${_.repelling?"repelling":""}`,style:{left:`${_.leftPercent}%`,top:`${_.topPercent}%`,width:`${_.size}px`,height:`${_.size}px`,backgroundColor:_.color,boxShadow:`0 0 5px 1px ${_.glowColor}`,animationName:_.repelling?"none":void 0,animationDuration:_.animationDuration,animationDelay:_.animationDelay,transition:_.repelling?"left 0.1s ease-out, top 0.1s ease-out":"none"}},_.id))})},Qm=()=>i.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4 relative overflow-hidden",children:[i.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[i.jsx("div",{className:"absolute -inset-[10%] bg-gradient-to-tr from-green-500/5 via-gree-300/10 to-transparent rotate-12 transform-gpu blur-3xl"}),i.jsx("div",{className:"absolute -inset-[5%] top-[30%] bg-gradient-to-bl from-green-500/5 via-gree-400/5 to-transparent -rotate-12 transform-gpu blur-2xl"})]}),i.jsx($m,{count:100,color:"rgba(74, 222, 128, 0.8)",glowColor:"rgba(74, 222, 128, 0.5)",minSize:1.5,maxSize:3.5,minDuration:4,maxDuration:8,topOffset:5,concentrateTop:!1,mouseRepelRadius:0,mouseRepelStrength:0}),i.jsx("div",{className:"relative z-10 w-full max-w-7xl mx-auto flex justify-center",children:i.jsx(rf,{})})]}),Km=()=>{const l=Gr(),s=()=>{localStorage.removeItem("isAuthenticated"),l("/",{replace:!0})};return i.jsxs("div",{className:"min-h-screen bg-gray-900 flex relative overflow-hidden",children:[i.jsxs("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[i.jsx("div",{className:"absolute -inset-[10%] bg-gradient-to-tr from-green-500/5 via-green-300/10 to-transparent rotate-12 transform-gpu blur-3xl"}),i.jsx("div",{className:"absolute -inset-[5%] top-[30%] bg-gradient-to-bl from-green-500/5 via-green-400/5 to-transparent -rotate-12 transform-gpu blur-2xl"})]}),i.jsxs("aside",{className:"w-64 bg-gray-800/80 backdrop-blur-sm border-r border-gray-700/50 text-white p-4 relative z-10",children:[i.jsxs("div",{className:"mb-8 flex items-center",children:[i.jsx("div",{className:"bg-green-500 w-8 h-8 rounded-lg mr-3 flex items-center justify-center shadow-lg shadow-green-500/20",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-900",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})}),i.jsx("h1",{className:"text-2xl font-bold",children:"CodeSpace"})]}),i.jsxs("nav",{className:"space-y-1",children:[i.jsxs(di,{to:"/dashboard",className:"flex items-center py-2 px-4 rounded-lg hover:bg-gray-700/70 hover:text-green-400 transition-colors duration-200",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),"Dashboard"]}),i.jsxs(di,{to:"/profile",className:"flex items-center py-2 px-4 rounded-lg hover:bg-gray-700/70 hover:text-green-400 transition-colors duration-200",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Profile"]}),i.jsxs(di,{to:"/settings",className:"flex items-center py-2 px-4 rounded-lg hover:bg-gray-700/70 hover:text-green-400 transition-colors duration-200",children:[i.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Settings"]}),i.jsxs(di,{to:"/change-password",className:"flex items-center py-2 px-4 rounded-lg hover:bg-gray-700/70 hover:text-green-400 transition-colors duration-200",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),"Change Password"]}),i.jsx("div",{className:"pt-4 mt-4 border-t border-gray-700/50",children:i.jsxs("button",{onClick:s,className:"w-full flex items-center text-left py-2 px-4 rounded-lg hover:bg-gray-700/70 text-red-400 hover:text-red-300 transition-colors duration-200",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Logout"]})})]})]}),i.jsx("main",{className:"flex-grow p-8 bg-gray-900/80 backdrop-blur-sm text-white relative z-10",children:i.jsx(rf,{})})]})},Ym={},Pd=l=>{let s;const a=new Set,c=(k,j)=>{const P=typeof k=="function"?k(s):k;if(!Object.is(P,s)){const D=s;s=j??(typeof P!="object"||P===null)?P:Object.assign({},s,P),a.forEach(U=>U(s,D))}},d=()=>s,p={setState:c,getState:d,getInitialState:()=>S,subscribe:k=>(a.add(k),()=>a.delete(k)),destroy:()=>{(Ym?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),a.clear()}},S=s=l(c,d,p);return p},Gm=l=>l?Pd(l):Pd;var fs={exports:{}},hs={},ms={exports:{}},ps={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cd;function Jm(){if(Cd)return ps;Cd=1;var l=Gl();function s(j,P){return j===P&&(j!==0||1/j===1/P)||j!==j&&P!==P}var a=typeof Object.is=="function"?Object.is:s,c=l.useState,d=l.useEffect,h=l.useLayoutEffect,m=l.useDebugValue;function v(j,P){var D=P(),U=c({inst:{value:D,getSnapshot:P}}),F=U[0].inst,_=U[1];return h(function(){F.value=D,F.getSnapshot=P,p(F)&&_({inst:F})},[j,D,P]),d(function(){return p(F)&&_({inst:F}),j(function(){p(F)&&_({inst:F})})},[j]),m(D),D}function p(j){var P=j.getSnapshot;j=j.value;try{var D=P();return!a(j,D)}catch{return!0}}function S(j,P){return P()}var k=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?S:v;return ps.useSyncExternalStore=l.useSyncExternalStore!==void 0?l.useSyncExternalStore:k,ps}var bd;function Xm(){return bd||(bd=1,ms.exports=Jm()),ms.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ld;function qm(){if(Ld)return hs;Ld=1;var l=Gl(),s=Xm();function a(S,k){return S===k&&(S!==0||1/S===1/k)||S!==S&&k!==k}var c=typeof Object.is=="function"?Object.is:a,d=s.useSyncExternalStore,h=l.useRef,m=l.useEffect,v=l.useMemo,p=l.useDebugValue;return hs.useSyncExternalStoreWithSelector=function(S,k,j,P,D){var U=h(null);if(U.current===null){var F={hasValue:!1,value:null};U.current=F}else F=U.current;U=v(function(){function Z(ge){if(!$){if($=!0,K=ge,ge=P(ge),D!==void 0&&F.hasValue){var L=F.value;if(D(L,ge))return te=L}return te=ge}if(L=te,c(K,ge))return L;var B=P(ge);return D!==void 0&&D(L,B)?(K=ge,L):(K=ge,te=B)}var $=!1,K,te,X=j===void 0?null:j;return[function(){return Z(k())},X===null?void 0:function(){return Z(X())}]},[k,j,P,D]);var _=d(S,U[0],U[1]);return m(function(){F.hasValue=!0,F.value=_},[_]),p(_),_},hs}var Rd;function Zm(){return Rd||(Rd=1,fs.exports=qm()),fs.exports}var ep=Zm();const tp=ws(ep),lf={},{useDebugValue:rp}=vr,{useSyncExternalStoreWithSelector:np}=tp;let _d=!1;const lp=l=>l;function op(l,s=lp,a){(lf?"production":void 0)!=="production"&&a&&!_d&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),_d=!0);const c=np(l.subscribe,l.getState,l.getServerState||l.getInitialState,s,a);return rp(c),c}const Td=l=>{(lf?"production":void 0)!=="production"&&typeof l!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const s=typeof l=="function"?Gm(l):l,a=(c,d)=>op(s,c,d);return Object.assign(a,s),a},ip=l=>l?Td(l):Td,Jl="http://localhost:3000/api",of=()=>Jl,af=async(l,s=!1)=>{const a={"Content-Type":"application/json"};if(s){const c=localStorage.getItem("token");c&&(a.Authorization=`Bearer ${c}`)}try{const c=await fetch(`${Jl}${l}`,{method:"GET",headers:a}),d=await c.json();if(!c.ok)throw new Error(d.message||"Something went wrong");return d}catch(c){throw console.error(`Error in GET ${l}:`,c),c}},or=async(l,s,a=!1)=>{const c={"Content-Type":"application/json"};if(a){const d=localStorage.getItem("token");d&&(c.Authorization=`Bearer ${d}`)}try{const d=await fetch(`${Jl}${l}`,{method:"POST",headers:c,body:JSON.stringify(s)}),h=await d.json();if(!d.ok)throw new Error(h.message||"Something went wrong");return h}catch(d){throw console.error(`Error in POST ${l}:`,d),d}},sf=async(l,s,a=!1)=>{const c={"Content-Type":"application/json"};if(a){const d=localStorage.getItem("token");d&&(c.Authorization=`Bearer ${d}`)}try{const d=await fetch(`${Jl}${l}`,{method:"PUT",headers:c,body:JSON.stringify(s)}),h=await d.json();if(!d.ok)throw new Error(h.message||"Something went wrong");return h}catch(d){throw console.error(`Error in PUT ${l}:`,d),d}},Si=async(l,s=!1)=>{const a={"Content-Type":"application/json"};if(s){const c=localStorage.getItem("token");c&&(a.Authorization=`Bearer ${c}`)}try{const c=await fetch(`${Jl}${l}`,{method:"DELETE",headers:a}),d=await c.json();if(!c.ok)throw new Error(d.message||"Something went wrong");return d}catch(c){throw console.error(`Error in DELETE ${l}:`,c),c}},ap=async l=>await or("/auth/check-email",{email:l}),sp=async l=>await or("/auth/register",l),up=async(l,s)=>{const a=await or("/auth/verify-otp",{userId:l,otp:s});return a.token&&(localStorage.setItem("token",a.token),localStorage.setItem("user",JSON.stringify(a.user)),localStorage.setItem("isAuthenticated","true")),a},cp=async(l,s="verification")=>await or("/auth/resend-otp",{userId:l,purpose:s}),dp=async(l,s,a=!1)=>{const c=await or("/auth/login",{email:l,password:s,rememberMe:a});return c.token&&(localStorage.setItem("token",c.token),localStorage.setItem("user",JSON.stringify(c.user)),localStorage.setItem("isAuthenticated","true")),c},fp=()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),localStorage.removeItem("isAuthenticated")},hp=()=>{const l=localStorage.getItem("user");return l?JSON.parse(l):null},mp=()=>localStorage.getItem("isAuthenticated")==="true",pp=async()=>await af("/auth/me",!0),gp=async l=>await or("/auth/forgot-password",{email:l}),vp=async(l,s)=>await or("/auth/verify-reset-otp",{userId:l,otp:s}),yp=async(l,s,a)=>await or("/auth/reset-password",{userId:l,otp:s,newPassword:a}),xp=async(l,s)=>await sf("/auth/change-password",{currentPassword:l,newPassword:s},!0),wp=()=>`${of()}/auth/google`,uf=()=>{const l=wp();window.location.href=l},jp=()=>`${of()}/auth/github`,cf=()=>{const l=jp();window.location.href=l},Zn=ip((l,s)=>({user:hp(),isAuthenticated:mp(),isLoading:!1,error:null,registrationData:null,checkEmail:async a=>{try{l({isLoading:!0,error:null});const c=await ap(a);return l({isLoading:!1}),c}catch(c){throw l({isLoading:!1,error:c.message}),c}},register:async a=>{try{l({isLoading:!0,error:null});const c=await sp(a);return l({isLoading:!1,registrationData:{userId:c.userId,email:a.email}}),c}catch(c){throw l({isLoading:!1,error:c.message}),c}},verifyOTP:async(a,c)=>{try{l({isLoading:!0,error:null});const d=await up(a,c);return l({isLoading:!1,isAuthenticated:!0,user:d.user,registrationData:null}),d}catch(d){throw l({isLoading:!1,error:d.message}),d}},resendOTP:async(a,c="verification")=>{try{l({isLoading:!0,error:null});const d=await cp(a,c);return l({isLoading:!1}),d}catch(d){throw l({isLoading:!1,error:d.message}),d}},login:async(a,c,d=!1)=>{try{l({isLoading:!0,error:null});const h=await dp(a,c,d);return l({isLoading:!1,isAuthenticated:!0,user:h.user}),h}catch(h){throw l({isLoading:!1,error:h.message}),h}},logout:()=>{fp(),l({isAuthenticated:!1,user:null})},getProfile:async()=>{try{l({isLoading:!0,error:null});const a=await pp();return l({isLoading:!1,user:a.user}),a}catch(a){throw l({isLoading:!1,error:a.message}),a}},forgotPassword:async a=>{try{l({isLoading:!0,error:null});const c=await gp(a);return l({isLoading:!1}),c}catch(c){throw l({isLoading:!1,error:c.message}),c}},verifyResetOTP:async(a,c)=>{try{l({isLoading:!0,error:null});const d=await vp(a,c);return l({isLoading:!1}),d}catch(d){throw l({isLoading:!1,error:d.message}),d}},resetPassword:async(a,c,d)=>{try{l({isLoading:!0,error:null});const h=await yp(a,c,d);return l({isLoading:!1}),h}catch(h){throw l({isLoading:!1,error:h.message}),h}},changePassword:async(a,c)=>{try{l({isLoading:!0,error:null});const d=await xp(a,c);return l({isLoading:!1}),d}catch(d){throw l({isLoading:!1,error:d.message}),d}},setAuthState:a=>l(a),clearError:()=>l({error:null})})),df=({userId:l,email:s,onBackToRegister:a})=>{const[c,d]=C.useState(["","","","","",""]),[h,m]=C.useState(""),[v,p]=C.useState(""),[S,k]=C.useState(600),[j,P]=C.useState(!1),D=C.useRef([]),U=Gr(),{verifyOTP:F,resendOTP:_,isLoading:Z}=Zn();C.useEffect(()=>{if(S<=0)return;const B=setTimeout(()=>{k(S-1)},1e3);return()=>clearTimeout(B)},[S]);const $=B=>{const q=Math.floor(B/60),Y=B%60;return`${q.toString().padStart(2,"0")}:${Y.toString().padStart(2,"0")}`},K=(B,q)=>{if(!/^[0-9]*$/.test(q))return;const Y=[...c];Y[B]=q,d(Y),h&&m(""),q&&B<5&&D.current[B+1].focus()},te=(B,q)=>{q.key==="Backspace"&&!c[B]&&B>0&&D.current[B-1].focus()},X=B=>{B.preventDefault();const q=B.clipboardData.getData("text");if(/^\d{6}$/.test(q)){const Y=q.split("");d(Y),D.current[5].focus()}},ge=async()=>{try{if(c.some(q=>!q)){m("Please enter the complete 6-digit OTP");return}const B=c.join("");await F(l,B),p("Verification successful! Logging you in..."),setTimeout(()=>{U("/dashboard",{replace:!0}),window.history.pushState(null,"","/dashboard");const q=()=>{window.history.pushState(null,"","/dashboard")};window.addEventListener("popstate",q),window._preventBackNavigation=q},1e3)}catch(B){m(B.message||"Failed to verify OTP. Please try again.")}},L=async()=>{try{P(!0),p(""),m(""),await _(l),k(600),p("OTP has been resent to your email")}catch(B){m(B.message||"Failed to resend OTP. Please try again.")}finally{P(!1)}};return i.jsxs("div",{className:"auth-form-container",children:[i.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"Verify Your Email"}),i.jsxs("p",{className:"text-gray-400 mb-6",children:["We've sent a verification code to ",i.jsx("span",{className:"text-green-400",children:s})]}),h&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:h})}),v&&i.jsx("div",{className:"bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:v})}),i.jsxs("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-gray-300 mb-2",children:"Enter 6-digit OTP"}),i.jsx("div",{className:"flex gap-2 justify-between",children:c.map((B,q)=>i.jsx("input",{ref:Y=>D.current[q]=Y,type:"text",maxLength:1,value:B,onChange:Y=>K(q,Y.target.value),onKeyDown:Y=>te(q,Y),onPaste:q===0?X:null,className:"w-12 h-12 text-center text-xl bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-white"},q))}),i.jsxs("div",{className:"mt-2 text-gray-400 text-sm flex justify-between",children:[i.jsxs("span",{children:["Time remaining: ",$(S)]}),i.jsx("button",{type:"button",onClick:L,disabled:j||S>0,className:`text-green-400 hover:text-green-300 ${j||S>0?"opacity-50 cursor-not-allowed":""}`,children:j?"Resending...":"Resend OTP"})]})]}),i.jsxs("div",{className:"flex flex-col space-y-4",children:[i.jsxs("button",{type:"button",onClick:ge,disabled:Z||c.some(B=>!B),className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:Z?"Verifying...":"Verify OTP"})]}),i.jsx("button",{type:"button",onClick:a,className:"w-full flex justify-center py-3 px-4 border border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-transparent hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:"Back to Registration"})]})]})},ff=({onRegisterClick:l,onForgotClick:s,onBackToMenuClick:a,initialError:c=""})=>{const[d,h]=C.useState({email:"",password:"",rememberMe:!1}),[m,v]=C.useState(c),[p,S]=C.useState(!1),[k,j]=C.useState(null),P=Gr(),{login:D,isLoading:U,error:F}=Zn();C.useEffect(()=>{F&&v(F)},[F]);const _=$=>{const{name:K,value:te,type:X,checked:ge}=$.target;h(L=>({...L,[K]:X==="checkbox"?ge:te})),m&&v("")},Z=async $=>{if($.preventDefault(),v(""),!d.email||!d.password){v("Please fill in all fields");return}try{const K=await D(d.email,d.password,d.rememberMe);if(K.requiresVerification){j(K.userId),S(!0);return}P("/dashboard",{replace:!0}),window.history.pushState(null,"","/dashboard");const te=()=>{window.history.pushState(null,"","/dashboard")};window.addEventListener("popstate",te),window._preventBackNavigation=te}catch(K){v(K.message||"Login failed. Please try again.")}};return p&&k?i.jsx(df,{userId:k,email:d.email,onBackToRegister:()=>S(!1)}):i.jsxs("div",{className:"auth-form-container",children:[i.jsx("h2",{className:"text-3xl font-bold text-white mb-2 text-center",children:"Welcome Back"}),i.jsx("p",{className:"text-gray-400 mb-6 text-center",children:"Sign in to your account to continue"}),m&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:m})}),i.jsxs("form",{onSubmit:Z,className:"space-y-5",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-1",children:"Email"}),i.jsx("input",{type:"email",id:"email",name:"email",value:d.email,onChange:_,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-1",children:"Password"}),i.jsx("input",{type:"password",id:"password",name:"password",value:d.password,onChange:_,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{id:"rememberMe",name:"rememberMe",type:"checkbox",checked:d.rememberMe,onChange:_,className:"h-4 w-4 text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600 rounded"}),i.jsx("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-gray-300",children:"Remember me"})]}),i.jsx("button",{type:"button",onClick:s,className:"text-sm text-green-400 hover:text-green-300 font-medium",children:"Forgot password?"})]}),i.jsx("div",{children:i.jsxs("button",{type:"submit",disabled:U,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:U?"Signing in...":"Sign in"})]})})]}),i.jsxs("div",{className:"relative my-6",children:[i.jsx("div",{className:"absolute inset-0 flex items-center",children:i.jsx("div",{className:"w-full border-t border-gray-600"})}),i.jsx("div",{className:"relative flex justify-center text-sm",children:i.jsx("span",{className:"px-2 bg-gray-800 text-gray-400",children:"Or continue with"})})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-6",children:[i.jsxs("button",{type:"button",onClick:()=>uf(),className:"flex items-center justify-center py-2.5 px-4 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:[i.jsxs("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[i.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),i.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),i.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),i.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google"]}),i.jsxs("button",{type:"button",onClick:()=>cf(),className:"flex items-center justify-center py-2.5 px-4 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:[i.jsx("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"})}),"GitHub"]})]}),i.jsxs("div",{className:"text-center space-y-3",children:[i.jsxs("p",{className:"text-sm text-gray-400",children:["Don't have an account?"," ",i.jsx("button",{onClick:l,className:"font-medium text-green-400 hover:text-green-300",children:"Sign up"})]}),i.jsx("p",{className:"text-sm",children:i.jsxs("button",{onClick:a,className:"text-gray-400 hover:text-gray-300 flex items-center justify-center mx-auto",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Menu"]})})]})]})},hf=({onLoginClick:l,onBackToMenuClick:s,initialError:a=""})=>{const[c,d]=C.useState({name:"",email:"",password:"",confirmPassword:""}),[h,m]=C.useState(a),[v,p]=C.useState(""),[S,k]=C.useState(!1),[j,P]=C.useState(!1),[D,U]=C.useState(!1),[F,_]=C.useState(null),{register:Z,checkEmail:$,isLoading:K,error:te}=Zn();C.useEffect(()=>{te&&m(te)},[te]);const X=B=>{const{name:q,value:Y}=B.target;d(Se=>({...Se,[q]:Y})),h&&m(""),q==="email"&&Y.includes("@")&&ge(Y)},ge=async B=>{try{if(!B||!B.includes("@")||!B.includes(".")){P(!1);return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(B)){P(!1);return}k(!0),P(!1),(await $(B)).exists?(m("Email is already registered. Please use a different email or sign in."),P(!1)):(P(!0),h!=null&&h.includes("Email is already registered")&&m(""))}catch(q){console.error("Email check error:",q),P(!1)}finally{k(!1)}},L=async B=>{if(B.preventDefault(),m(""),!c.name||!c.email||!c.password||!c.confirmPassword){m("Please fill in all fields");return}if(c.password!==c.confirmPassword){m("Passwords do not match");return}if(c.password.length<8){m("Password must be at least 8 characters long");return}try{p("");const q=await Z({name:c.name,email:c.email,password:c.password});p("Registration successful! Please verify your email with the OTP sent."),setTimeout(()=>{_(q.userId),U(!0)},1500)}catch(q){m(q.message||"Registration failed. Please try again.")}};return D&&F?i.jsx(df,{userId:F,email:c.email,onBackToRegister:()=>U(!1)}):i.jsxs("div",{className:"auth-form-container",children:[i.jsx("h2",{className:"text-3xl font-bold text-white mb-2 text-center",children:"Create an Account"}),i.jsx("p",{className:"text-gray-400 mb-6 text-center",children:"Join CodeSpace and start coding today"}),h&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:h})}),v&&i.jsx("div",{className:"bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:v})}),i.jsxs("form",{onSubmit:L,className:"space-y-5",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-300 mb-1",children:"Full Name"}),i.jsx("input",{type:"text",id:"name",name:"name",value:c.name,onChange:X,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-1",children:"Email"}),i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:"email",id:"email",name:"email",value:c.email,onChange:X,className:`w-full px-3 py-2 bg-gray-700 border ${h!=null&&h.includes("Email is already registered")?"border-red-500":j?"border-green-500":"border-gray-600"} text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 pr-10`,required:!0}),S&&i.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:i.jsxs("svg",{className:"animate-spin h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),!S&&j&&i.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:i.jsx("svg",{className:"h-5 w-5 text-green-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),!S&&(h==null?void 0:h.includes("Email is already registered"))&&i.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:i.jsx("svg",{className:"h-5 w-5 text-red-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})]}),(h==null?void 0:h.includes("Email is already registered"))&&i.jsx("p",{className:"mt-1 text-sm text-red-500",children:h}),j&&i.jsx("p",{className:"mt-1 text-sm text-green-500",children:"Email is available"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-1",children:"Password"}),i.jsx("input",{type:"password",id:"password",name:"password",value:c.password,onChange:X,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300 mb-1",children:"Confirm Password"}),i.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:c.confirmPassword,onChange:X,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsx("div",{children:i.jsxs("button",{type:"submit",disabled:K||S||(h==null?void 0:h.includes("Email is already registered")),className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:K?"Creating Account...":"Create Account"})]})})]}),i.jsxs("div",{className:"relative my-6",children:[i.jsx("div",{className:"absolute inset-0 flex items-center",children:i.jsx("div",{className:"w-full border-t border-gray-600"})}),i.jsx("div",{className:"relative flex justify-center text-sm",children:i.jsx("span",{className:"px-2 bg-gray-800 text-gray-400",children:"Or continue with"})})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-6",children:[i.jsxs("button",{type:"button",onClick:()=>uf(),className:"flex items-center justify-center py-2.5 px-4 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:[i.jsxs("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[i.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),i.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),i.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),i.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google"]}),i.jsxs("button",{type:"button",onClick:()=>cf(),className:"flex items-center justify-center py-2.5 px-4 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:[i.jsx("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"})}),"GitHub"]})]}),i.jsxs("div",{className:"text-center space-y-3",children:[i.jsxs("p",{className:"text-sm text-gray-400",children:["Already have an account?"," ",i.jsx("button",{onClick:l,className:"font-medium text-green-400 hover:text-green-300",children:"Sign in"})]}),i.jsx("p",{className:"text-sm",children:i.jsxs("button",{onClick:s,className:"text-gray-400 hover:text-gray-300 flex items-center justify-center mx-auto",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Menu"]})})]})]})},mf=({onLoginClick:l})=>{const[s,a]=C.useState(1),[c,d]=C.useState(""),[h,m]=C.useState(["","","","","",""]),[v,p]=C.useState(null),[S,k]=C.useState(""),[j,P]=C.useState(""),[D,U]=C.useState(""),[F,_]=C.useState(""),[Z,$]=C.useState(600),[K,te]=C.useState(!1),X=C.useRef([]),{forgotPassword:ge,resetPassword:L,verifyResetOTP:B,resendOTP:q,isLoading:Y,error:Se}=Zn();C.useEffect(()=>{Se&&_(Se)},[Se]),C.useEffect(()=>{if(Z<=0||s!==2)return;const I=setTimeout(()=>{$(Z-1)},1e3);return()=>clearTimeout(I)},[Z,s]);const ke=I=>{const y=Math.floor(I/60),O=I%60;return`${y.toString().padStart(2,"0")}:${O.toString().padStart(2,"0")}`},Ce=(I,y)=>{if(!/^[0-9]*$/.test(y))return;const O=[...h];O[I]=y,m(O),F&&_(""),y&&I<5&&X.current[I+1].focus()},Te=(I,y)=>{y.key==="Backspace"&&!h[I]&&I>0&&X.current[I-1].focus()},We=I=>{I.preventDefault();const y=I.clipboardData.getData("text");if(/^\d{6}$/.test(y)){const O=y.split("");m(O),X.current[5].focus()}},Ke=async()=>{try{te(!0),U(""),_(""),await q(v,"password-reset"),$(600),U("OTP has been resent to your email")}catch(I){_(I.message||"Failed to resend OTP. Please try again.")}finally{te(!1)}},ue=async I=>{if(I.preventDefault(),_(""),U(""),!c){_("Please enter your email address");return}try{const y=await ge(c);p(y.userId),U("OTP has been sent to your email"),$(600),a(2)}catch(y){_(y.message||"Failed to send OTP. Please try again.")}},M=async I=>{if(I.preventDefault(),_(""),U(""),h.some(y=>!y)){_("Please enter the complete 6-digit OTP");return}try{const y=h.join("");await B(v,y),U("OTP verified successfully"),setTimeout(()=>{a(3)},1e3)}catch(y){_(y.message||"Invalid OTP. Please try again.")}},J=async I=>{if(I.preventDefault(),_(""),U(""),!S||!j){_("Please fill in all fields");return}if(S.length<8){_("Password must be at least 8 characters long");return}if(S!==j){_("Passwords do not match");return}try{const y=h.join("");await L(v,y,S),U("Password has been reset successfully"),a(4)}catch(y){_(y.message||"Failed to reset password. Please try again.")}};return i.jsxs("div",{className:"auth-form-container",children:[i.jsx("h2",{className:"text-3xl font-bold text-white mb-2 text-center",children:"Reset Your Password"}),i.jsxs("p",{className:"text-gray-400 mb-6 text-center",children:[s===1&&"Enter your email to receive a verification code",s===2&&"Enter the verification code sent to your email",s===3&&"Create a new password for your account",s===4&&"Your password has been reset successfully"]}),F&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:F})}),D&&i.jsx("div",{className:"text-center bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:D})}),s===1&&i.jsxs("form",{onSubmit:ue,className:"space-y-5",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-1",children:"Email"}),i.jsx("input",{type:"email",id:"email",value:c,onChange:I=>d(I.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsx("div",{children:i.jsxs("button",{type:"submit",disabled:Y,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group disabled:opacity-50 disabled:cursor-not-allowed",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:Y?"Sending...":"Send Verification Code"})]})})]}),s===2&&i.jsxs("form",{onSubmit:M,className:"space-y-5",children:[i.jsxs("div",{className:"mb-6",children:[i.jsx("label",{className:"block text-gray-300 mb-2",children:"Enter 6-digit OTP"}),i.jsx("div",{className:"flex gap-2 justify-between",children:h.map((I,y)=>i.jsx("input",{ref:O=>X.current[y]=O,type:"text",maxLength:1,value:I,onChange:O=>Ce(y,O.target.value),onKeyDown:O=>Te(y,O),onPaste:y===0?We:null,className:"w-12 h-12 text-center text-xl bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-white"},y))}),i.jsxs("div",{className:"mt-2 text-gray-400 text-sm flex justify-between",children:[i.jsxs("span",{children:["Time remaining: ",ke(Z)]}),i.jsx("button",{type:"button",onClick:Ke,disabled:K||Z>0,className:`text-green-400 hover:text-green-300 ${K||Z>0?"opacity-50 cursor-not-allowed":""}`,children:K?"Resending...":"Resend OTP"})]})]}),i.jsxs("div",{className:"flex flex-col space-y-3",children:[i.jsxs("button",{type:"submit",disabled:Y||h.some(I=>!I),className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:Y?"Verifying...":"Verify OTP"})]}),i.jsx("button",{type:"button",onClick:()=>a(1),className:"w-full flex justify-center py-3 px-4 border border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-transparent hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:"Back to Email"})]})]}),s===3&&i.jsxs("form",{onSubmit:J,className:"space-y-5",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-300 mb-1",children:"New Password"}),i.jsx("input",{type:"password",id:"newPassword",value:S,onChange:I=>k(I.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300 mb-1",children:"Confirm Password"}),i.jsx("input",{type:"password",id:"confirmPassword",value:j,onChange:I=>P(I.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{className:"flex flex-col space-y-3",children:[i.jsxs("button",{type:"submit",disabled:Y,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:Y?"Resetting...":"Reset Password"})]}),i.jsx("button",{type:"button",onClick:()=>a(2),className:"w-full flex justify-center py-3 px-4 border border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-transparent hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:"Back to Verification"})]})]}),s===4&&i.jsxs("div",{className:"text-center bg-gray-700/50 p-6 rounded-lg border border-gray-600",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-green-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),i.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Password Reset Successful"}),i.jsx("p",{className:"text-gray-300 mb-4",children:"Your password has been reset successfully. You can now log in with your new password."}),i.jsxs("button",{onClick:l,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Go to Login"})]})]}),s!==4&&i.jsx("div",{className:"mt-6 text-center",children:i.jsx("p",{className:"text-sm text-gray-400",children:i.jsx("button",{onClick:l,className:"font-medium text-green-400 hover:text-green-300",children:"Back to Sign in"})})})]})},Md=({onLoginClick:l,onRegisterClick:s})=>{const[a,c]=C.useState(""),[d,h]=C.useState(""),m=p=>{c(p.target.value),d&&h("")},v=p=>{if(p.preventDefault(),!a.trim()){h("Please enter a valid URL");return}console.log(`Accessing codespace at: ${a}`)};return i.jsxs("div",{className:"auth-form-container",children:[i.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"Welcome to CodeSpace"}),i.jsx("p",{className:"text-gray-400 mb-6",children:"Your collaborative coding platform"}),d&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:d})}),i.jsx("form",{onSubmit:v,className:"space-y-5 mb-6",children:i.jsxs("div",{children:[i.jsx("label",{htmlFor:"codespace-url",className:"block text-sm font-medium text-gray-300 mb-1",children:"Access a Public Codespace"}),i.jsxs("div",{className:"flex",children:[i.jsx("input",{type:"text",id:"codespace-url",name:"codespace-url",value:a,onChange:m,placeholder:"Enter codespace URL",className:"flex-grow px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-l-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"}),i.jsxs("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-r-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Access"})]})]}),i.jsx("p",{className:"mt-1 text-xs text-gray-400",children:"Enter a URL to access a public codespace as a guest"})]})}),i.jsxs("div",{className:"relative my-8",children:[i.jsx("div",{className:"absolute inset-0 flex items-center",children:i.jsx("div",{className:"w-full border-t border-gray-600"})}),i.jsx("div",{className:"relative flex justify-center text-sm",children:i.jsx("span",{className:"px-2 bg-gray-800 text-gray-400",children:"Or"})})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("button",{onClick:l,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Sign in to your account"})]}),i.jsx("button",{onClick:s,className:"w-full flex justify-center py-3 px-4 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:"Create a new account"})]})]})},Sp=()=>{const[l]=nf(),[s,a]=C.useState("menu"),[c,d]=C.useState(!1),[h,m]=C.useState("menu"),[v,p]=C.useState("");C.useEffect(()=>{const k=l.get("error"),j=l.get("message");if(k)if(a("login"),j)p(decodeURIComponent(j));else switch(k){case"google_auth_failed":p("Google authentication failed. Please try again or use email/password.");break;case"email_already_linked":p("This email is already linked to a different Google account.");break;case"auth_record_not_found":p("Authentication record not found. Please try again or contact support.");break;default:p("Authentication failed. Please try again.")}},[l]),C.useEffect(()=>{if(s!==h){d(!0);const k=setTimeout(()=>{m(s),d(!1)},300);return()=>clearTimeout(k)}},[s,h]);const S=()=>{switch(h){case"menu":return i.jsx(Md,{onLoginClick:()=>a("login"),onRegisterClick:()=>a("register")});case"login":return i.jsx(ff,{onRegisterClick:()=>a("register"),onForgotClick:()=>a("forgot"),onBackToMenuClick:()=>a("menu"),initialError:v});case"register":return i.jsx(hf,{onLoginClick:()=>a("login"),onBackToMenuClick:()=>a("menu"),initialError:v});case"forgot":return i.jsx(mf,{onLoginClick:()=>a("login")});default:return i.jsx(Md,{onLoginClick:()=>a("login"),onRegisterClick:()=>a("register")})}};return i.jsxs("div",{className:"w-full flex flex-col md:flex-row gap-8 md:gap-16",children:[i.jsx("div",{className:"w-full md:w-1/2 flex flex-col justify-center",children:i.jsxs("div",{className:"text-white",children:[i.jsxs("div",{className:"flex items-center mb-6",children:[i.jsx("div",{className:"bg-green-500 w-10 h-10 rounded-lg mr-3 flex items-center justify-center",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-gray-900",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})}),i.jsx("h1",{className:"text-4xl md:text-5xl font-bold",children:"CodeSpace"})]}),i.jsx("h2",{className:"text-2xl md:text-3xl font-semibold mb-4 text-green-400",children:"Your Collaborative Coding Platform"}),i.jsx("p",{className:"text-gray-300 mb-8 text-lg leading-relaxed",children:"Write, share, and collaborate on code in real-time. Join thousands of developers who trust CodeSpace for their coding needs."}),i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"flex items-start group",children:[i.jsx("div",{className:"bg-gray-800 border border-green-500 rounded-lg p-2 mr-4 group-hover:bg-green-500/20 transition-colors duration-300",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-lg font-semibold text-green-400",children:"Real-time collaboration"}),i.jsx("p",{className:"text-gray-400",children:"Work together with your team in real-time, seeing changes as they happen."})]})]}),i.jsxs("div",{className:"flex items-start group",children:[i.jsx("div",{className:"bg-gray-800 border border-green-500 rounded-lg p-2 mr-4 group-hover:bg-green-500/20 transition-colors duration-300",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-lg font-semibold text-green-400",children:"Multiple language support"}),i.jsx("p",{className:"text-gray-400",children:"Code in JavaScript, Python, Java, C++, and many more languages."})]})]}),i.jsxs("div",{className:"flex items-start group",children:[i.jsx("div",{className:"bg-gray-800 border border-green-500 rounded-lg p-2 mr-4 group-hover:bg-green-500/20 transition-colors duration-300",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-lg font-semibold text-green-400",children:"Secure cloud storage"}),i.jsx("p",{className:"text-gray-400",children:"Your code is securely stored in the cloud, accessible from anywhere."})]})]})]})]})}),i.jsx("div",{className:"w-full md:w-1/2 flex items-center justify-center",children:i.jsx("div",{className:"w-full max-w-md p-8 rounded-xl border border-gray-700 shadow-2xl relative z-20",children:i.jsx("div",{className:`transition-opacity duration-300 ${c?"opacity-0":"opacity-100"}`,children:S()})})})]})},kp=async l=>{try{return await sf("/users/profile",l,!0)}catch(s){throw new Error(s.message||"Failed to update profile")}},Np=async l=>{try{return l?await or("/users/profile-picture",{imageUrl:l},!0):await Si("/users/profile-picture",!0)}catch(s){throw new Error(s.message||"Failed to upload profile picture")}},Ep=async()=>{try{return await Si("/users/profile-picture",!0)}catch(l){throw new Error(l.message||"Failed to delete profile picture")}},Pp=async l=>{try{return l?await or("/users/cover-photo",{imageUrl:l},!0):await Si("/users/cover-photo",!0)}catch(s){throw new Error(s.message||"Failed to upload cover photo")}},Cp=async()=>{try{return await Si("/users/cover-photo",!0)}catch(l){throw new Error(l.message||"Failed to delete cover photo")}};var pf={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},zd=vr.createContext&&vr.createContext(pf),bp=["attr","size","title"];function Lp(l,s){if(l==null)return{};var a=Rp(l,s),c,d;if(Object.getOwnPropertySymbols){var h=Object.getOwnPropertySymbols(l);for(d=0;d<h.length;d++)c=h[d],!(s.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(l,c)&&(a[c]=l[c])}return a}function Rp(l,s){if(l==null)return{};var a={};for(var c in l)if(Object.prototype.hasOwnProperty.call(l,c)){if(s.indexOf(c)>=0)continue;a[c]=l[c]}return a}function gi(){return gi=Object.assign?Object.assign.bind():function(l){for(var s=1;s<arguments.length;s++){var a=arguments[s];for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(l[c]=a[c])}return l},gi.apply(this,arguments)}function Dd(l,s){var a=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);s&&(c=c.filter(function(d){return Object.getOwnPropertyDescriptor(l,d).enumerable})),a.push.apply(a,c)}return a}function vi(l){for(var s=1;s<arguments.length;s++){var a=arguments[s]!=null?arguments[s]:{};s%2?Dd(Object(a),!0).forEach(function(c){_p(l,c,a[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(a)):Dd(Object(a)).forEach(function(c){Object.defineProperty(l,c,Object.getOwnPropertyDescriptor(a,c))})}return l}function _p(l,s,a){return s=Tp(s),s in l?Object.defineProperty(l,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[s]=a,l}function Tp(l){var s=Mp(l,"string");return typeof s=="symbol"?s:s+""}function Mp(l,s){if(typeof l!="object"||!l)return l;var a=l[Symbol.toPrimitive];if(a!==void 0){var c=a.call(l,s);if(typeof c!="object")return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return(s==="string"?String:Number)(l)}function gf(l){return l&&l.map((s,a)=>vr.createElement(s.tag,vi({key:a},s.attr),gf(s.child)))}function Es(l){return s=>vr.createElement(zp,gi({attr:vi({},l.attr)},s),gf(l.child))}function zp(l){var s=a=>{var{attr:c,size:d,title:h}=l,m=Lp(l,bp),v=d||a.size||"1em",p;return a.className&&(p=a.className),l.className&&(p=(p?p+" ":"")+l.className),vr.createElement("svg",gi({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,c,m,{className:p,style:vi(vi({color:l.color||a.color},a.style),l.style),height:v,width:v,xmlns:"http://www.w3.org/2000/svg"}),h&&vr.createElement("title",null,h),l.children)};return zd!==void 0?vr.createElement(zd.Consumer,null,a=>s(a)):s(pf)}function Dp(l){return Es({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M512 144v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V144c0-26.5 21.5-48 48-48h88l12.3-32.9c7-18.7 24.9-31.1 44.9-31.1h125.5c20 0 37.9 12.4 44.9 31.1L376 96h88c26.5 0 48 21.5 48 48zM376 288c0-66.2-53.8-120-120-120s-120 53.8-120 120 53.8 120 120 120 120-53.8 120-120zm-32 0c0 48.5-39.5 88-88 88s-88-39.5-88-88 39.5-88 88-88 88 39.5 88 88z"},child:[]}]})(l)}function Op(l){return Es({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 448H48c-26.51 0-48-21.49-48-48V112c0-26.51 21.49-48 48-48h416c26.51 0 48 21.49 48 48v288c0 26.51-21.49 48-48 48zM112 120c-30.928 0-56 25.072-56 56s25.072 56 56 56 56-25.072 56-56-25.072-56-56-56zM64 384h384V272l-87.515-87.515c-4.686-4.686-12.284-4.686-16.971 0L208 320l-55.515-55.515c-4.686-4.686-12.284-4.686-16.971 0L64 336v48z"},child:[]}]})(l)}function Fp(l){return Es({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"},child:[]}]})(l)}const Od=(l,s=1200,a=1200,c=1)=>new Promise(d=>{const h=new Image;h.src=l,h.onload=()=>{const m=document.createElement("canvas");let v=h.width,p=h.height;v>p?v>s&&(p=Math.round(p*s/v),v=s):p>a&&(v=Math.round(v*a/p),p=a),m.width=v,m.height=p,m.getContext("2d").drawImage(h,0,0,v,p);const k=m.toDataURL("image/jpeg",c);d(k)}}),Ip=()=>{var Ce,Te,We,Ke;const{user:l,getProfile:s,isLoading:a}=Zn(),[c,d]=C.useState({name:"",email:"",bio:"",title:"",location:"",website:"",dateOfBirth:"",socialLinks:{github:"",linkedin:"",twitter:"",instagram:"",youtube:""},skills:[]}),[h,m]=C.useState(!1),[v,p]=C.useState({...c,_skillsInputValue:""}),[S,k]=C.useState(""),[j,P]=C.useState(""),[D,U]=C.useState(""),[F,_]=C.useState(""),Z=C.useRef(null),$=C.useRef(null);C.useEffect(()=>{(async()=>{try{await s()}catch(M){console.error("Failed to load profile:",M),k("Failed to load profile data")}})()},[]),C.useEffect(()=>{var ue,M,J,I,y,O,fe,xe,ve,je;l&&(U(l.profilePicture||""),_(l.coverPhoto||""),d({name:l.name||"",email:l.email||"",bio:l.bio||"",title:l.title||"",location:l.location||"",website:l.website||"",dateOfBirth:l.dateOfBirth?new Date(l.dateOfBirth).toISOString().split("T")[0]:"",socialLinks:{github:((ue=l.socialLinks)==null?void 0:ue.github)||"",linkedin:((M=l.socialLinks)==null?void 0:M.linkedin)||"",twitter:((J=l.socialLinks)==null?void 0:J.twitter)||"",instagram:((I=l.socialLinks)==null?void 0:I.instagram)||"",youtube:((y=l.socialLinks)==null?void 0:y.youtube)||""},skills:l.skills||[]}),p({name:l.name||"",email:l.email||"",bio:l.bio||"",title:l.title||"",location:l.location||"",website:l.website||"",dateOfBirth:l.dateOfBirth?new Date(l.dateOfBirth).toISOString().split("T")[0]:"",socialLinks:{github:((O=l.socialLinks)==null?void 0:O.github)||"",linkedin:((fe=l.socialLinks)==null?void 0:fe.linkedin)||"",twitter:((xe=l.socialLinks)==null?void 0:xe.twitter)||"",instagram:((ve=l.socialLinks)==null?void 0:ve.instagram)||"",youtube:((je=l.socialLinks)==null?void 0:je.youtube)||""},skills:l.skills||[],_skillsInputValue:""}))},[l]);const K=ue=>{const{name:M,value:J}=ue.target;if(M.startsWith("social_")){const I=M.split("_")[1];p(y=>({...y,socialLinks:{...y.socialLinks,[I]:J}}))}else M==="skills"?J.includes(",")||p(I=>({...I,_skillsInputValue:J})):p(I=>({...I,[M]:J}))},te=ue=>{if(ue.key==="Enter"){ue.preventDefault();const M=ue.target.value.trim();M&&(v.skills.includes(M)||(p(J=>({...J,skills:[...J.skills,M],_skillsInputValue:""})),ue.target.value=""))}if(ue.key===","){ue.preventDefault();const M=ue.target.value.trim();M&&(v.skills.includes(M)||(p(J=>({...J,skills:[...J.skills,M],_skillsInputValue:""})),ue.target.value=""))}},X=ue=>{p(M=>({...M,skills:M.skills.filter(J=>J!==ue)}))},ge=()=>{Z.current.click()},L=async ue=>{ue.stopPropagation();try{const M=await Ep();console.log("Profile picture removal response:",M),U(""),P("Profile picture removed successfully"),await s()}catch(M){console.error("Failed to remove profile picture:",M),k(M.message||"Failed to remove profile picture")}},B=()=>{$.current.click()},q=async ue=>{ue.stopPropagation();try{const M=await Cp();console.log("Cover photo removal response:",M),_(""),P("Cover photo removed successfully"),await s()}catch(M){console.error("Failed to remove cover photo:",M),k(M.message||"Failed to remove cover photo")}},Y=async ue=>{const M=ue.target.files[0];if(!M)return;if(M.size>5*1024*1024){k("Image is too large. Please select an image under 5MB.");return}if(!M.type.match("image.*")){k("Please select an image file.");return}const J=new FileReader;J.onload=async I=>{try{const y=I.target.result,O=await Od(y,1920,600,.95);_(O);const fe=await Pp(O);console.log("Cover photo update response:",fe),P("Cover photo updated successfully"),await s()}catch(y){console.error("Failed to upload cover photo:",y),k(y.message||"Failed to upload cover photo")}},J.readAsDataURL(M)},Se=async ue=>{const M=ue.target.files[0];if(!M)return;if(M.size>5*1024*1024){k("Image is too large. Please select an image under 5MB.");return}if(!M.type.match("image.*")){k("Please select an image file.");return}const J=new FileReader;J.onload=async I=>{try{const y=I.target.result,O=await Od(y,1200,1200,.95);U(O);const fe=await Np(O);console.log("Profile picture update response:",fe),P("Profile picture updated successfully"),await s()}catch(y){console.error("Failed to upload profile picture:",y),k(y.message||"Failed to upload profile picture")}},J.readAsDataURL(M)},ke=async ue=>{ue.preventDefault(),k(""),P("");try{const M=await kp(v);console.log("Profile update response:",M),d(v),m(!1),P("Profile updated successfully"),await s()}catch(M){console.error("Failed to update profile:",M),k(M.message||"Failed to update profile")}};return i.jsxs("div",{className:"bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl border border-gray-700/50 shadow-xl",children:[i.jsxs("div",{className:"relative mb-8",children:[i.jsxs("div",{className:"relative h-60 rounded-t-xl overflow-hidden cursor-pointer group",onClick:B,children:[F?i.jsx("img",{src:F,alt:"Cover",className:"w-full h-full object-cover"}):i.jsx("div",{className:"w-full h-full bg-gradient-to-r from-gray-900 via-green-900/30 to-gray-900"}),i.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300",children:i.jsxs("div",{className:"flex space-x-3",children:[i.jsxs("div",{className:"bg-gray-800 bg-opacity-70 px-4 py-2 rounded-lg flex items-center cursor-pointer hover:bg-gray-700",children:[i.jsx(Op,{className:"text-white mr-2"}),i.jsx("span",{className:"text-white text-sm",children:"Change Cover Photo"})]}),F&&i.jsxs("div",{className:"bg-red-900 bg-opacity-70 px-4 py-2 rounded-lg flex items-center cursor-pointer hover:bg-red-800",onClick:q,children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-white",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})}),i.jsx("span",{className:"text-white text-sm",children:"Remove"})]})]})})]}),i.jsx("input",{type:"file",ref:$,className:"hidden",accept:"image/*",onChange:Y}),i.jsxs("div",{className:"relative z-10 flex flex-col items-center",children:[i.jsxs("div",{className:"relative w-36 h-36 rounded-full overflow-hidden border-4 border-gray-800 shadow-xl cursor-pointer group transform -translate-y-12 hover:scale-105 transition-all duration-300",onClick:ge,children:[D?i.jsx("img",{src:D,alt:"Profile",className:"w-full h-full object-cover"}):i.jsx("div",{className:"w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center",children:i.jsx(Fp,{className:"text-gray-400 text-4xl"})}),i.jsxs("div",{className:"absolute inset-0 bg-black bg-opacity-60 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out",children:[i.jsx(Dp,{className:"text-white text-2xl mb-2"}),D&&i.jsxs("button",{type:"button",className:"bg-red-900 bg-opacity-70 px-2 py-1 rounded text-xs text-white flex items-center hover:bg-red-800",onClick:L,children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})}),"Remove"]})]})]}),i.jsx("input",{type:"file",ref:Z,className:"hidden",accept:"image/*",onChange:Se}),i.jsx("p",{className:"text-sm text-gray-400 -mt-8 mb-2",children:"Click to change profile picture"})]})]}),i.jsxs("div",{className:"flex justify-between items-center mb-4",children:[i.jsx("h2",{className:"text-2xl font-bold text-white bg-clip-text text-transparent bg-gradient-to-r from-white to-green-400",children:"My Profile"}),!h&&i.jsxs("button",{onClick:()=>m(!0),className:"px-5 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-300 shadow-lg shadow-green-900/30 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsxs("span",{className:"relative flex items-center",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})}),"Edit Profile"]})]})]}),a&&i.jsx("div",{className:"flex justify-center items-center py-8",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"})}),S&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-2 rounded-lg mb-4 shadow-lg text-sm",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:S})}),j&&i.jsx("div",{className:"bg-green-900/50 border border-green-500 text-green-200 px-4 py-2 rounded-lg mb-4 shadow-lg text-sm",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:j})}),h?i.jsxs("form",{onSubmit:ke,className:"space-y-3",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-300 mb-1",children:"Full Name"}),i.jsx("input",{type:"text",id:"name",name:"name",value:v.name,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-1",children:"Email"}),i.jsx("input",{type:"email",id:"email",name:"email",value:v.email,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-300 mb-1",children:"Professional Title"}),i.jsx("input",{type:"text",id:"title",name:"title",value:v.title,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g. Full Stack Developer"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-300 mb-1",children:"Location"}),i.jsx("input",{type:"text",id:"location",name:"location",value:v.location,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g. New York, USA"})]})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-300 mb-1",children:"Bio"}),i.jsx("textarea",{id:"bio",name:"bio",rows:"3",value:v.bio,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"Tell us about yourself"})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"dateOfBirth",className:"block text-sm font-medium text-gray-300 mb-1",children:"Date of Birth"}),i.jsx("input",{type:"date",id:"dateOfBirth",name:"dateOfBirth",value:v.dateOfBirth,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-300 mb-1",children:"Website"}),i.jsx("input",{type:"url",id:"website",name:"website",value:v.website,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"https://yourwebsite.com"})]})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"skills",className:"block text-sm font-medium text-gray-300 mb-1",children:"Skills (comma separated)"}),i.jsxs("div",{className:"relative",children:[i.jsx("input",{type:"text",id:"skills",name:"skills",value:v._skillsInputValue||"",onChange:K,onKeyDown:te,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"e.g. JavaScript, React, Node.js"}),i.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:i.jsx("span",{className:"text-xs text-gray-400",children:"Press comma or Enter to add skills"})})]}),v.skills.length>0&&i.jsx("div",{className:"mt-2 flex flex-wrap gap-2",children:v.skills.map((ue,M)=>i.jsxs("span",{className:"px-3 py-1 bg-gray-800 text-green-400 rounded-lg text-sm border border-gray-700 flex items-center group cursor-pointer hover:bg-gray-700 transition-colors duration-200",onClick:()=>X(ue),title:"Click to remove",children:[ue,i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1.5 text-gray-400 group-hover:text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})]},M))})]}),i.jsxs("div",{className:"border-t border-gray-700 pt-4 mt-4",children:[i.jsx("h3",{className:"text-lg font-medium text-white mb-3",children:"Social Links"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"social_github",className:"block text-sm font-medium text-gray-300 mb-1",children:"GitHub"}),i.jsx("input",{type:"text",id:"social_github",name:"social_github",value:v.socialLinks.github,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"username or full profile URL"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"social_linkedin",className:"block text-sm font-medium text-gray-300 mb-1",children:"LinkedIn"}),i.jsx("input",{type:"text",id:"social_linkedin",name:"social_linkedin",value:v.socialLinks.linkedin,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"username"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"social_twitter",className:"block text-sm font-medium text-gray-300 mb-1",children:"Twitter"}),i.jsx("input",{type:"text",id:"social_twitter",name:"social_twitter",value:v.socialLinks.twitter,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"username"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"social_instagram",className:"block text-sm font-medium text-gray-300 mb-1",children:"Instagram"}),i.jsx("input",{type:"text",id:"social_instagram",name:"social_instagram",value:v.socialLinks.instagram,onChange:K,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",placeholder:"username"})]})]})]}),i.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[i.jsx("button",{type:"button",onClick:()=>{p({...c,_skillsInputValue:""}),m(!1)},className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200",children:"Cancel"}),i.jsxs("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Save Changes"})]})]})]}):i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Full Name"}),i.jsx("p",{className:"mt-1 text-lg font-semibold text-white",children:c.name})]}),i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Email"}),i.jsx("p",{className:"mt-1 text-lg font-semibold text-white",children:c.email})]})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Professional Title"}),i.jsx("p",{className:"mt-1 text-base text-white",children:c.title||i.jsx("span",{className:"text-gray-500 italic",children:"Not specified"})})]}),i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Location"}),i.jsx("p",{className:"mt-1 text-base text-white flex items-center",children:c.location?i.jsxs(i.Fragment,{children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),c.location]}):i.jsx("span",{className:"text-gray-500 italic",children:"Not specified"})})]})]}),i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Bio"}),i.jsx("p",{className:"mt-1 text-sm text-gray-300 leading-relaxed",children:c.bio||i.jsx("span",{className:"text-gray-500 italic",children:"No bio provided"})})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Date of Birth"}),i.jsx("p",{className:"mt-1 text-base text-white",children:c.dateOfBirth?i.jsxs(i.Fragment,{children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1 text-green-500 inline",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"})}),new Date(c.dateOfBirth).toLocaleDateString()]}):i.jsx("span",{className:"text-gray-500 italic",children:"Not specified"})})]}),i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider",children:"Website"}),i.jsx("p",{className:"mt-1 text-base",children:c.website?i.jsxs("a",{href:c.website,className:"text-green-400 hover:text-green-300 flex items-center group",target:"_blank",rel:"noopener noreferrer",children:[i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z",clipRule:"evenodd"})}),i.jsx("span",{className:"group-hover:underline truncate",children:c.website})]}):i.jsx("span",{className:"text-gray-500 italic",children:"Not specified"})})]})]}),i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider mb-2",children:"Skills"}),i.jsx("div",{className:"flex flex-wrap gap-1.5",children:c.skills&&c.skills.length>0?c.skills.map((ue,M)=>i.jsx("span",{className:"px-3 py-1 bg-gradient-to-r from-gray-800 to-gray-700 text-green-400 rounded-md text-xs font-medium shadow-sm hover:shadow-md transition-all duration-300 border border-gray-700",children:ue},M)):i.jsx("p",{className:"text-gray-500 italic text-sm",children:"No skills listed"})})]}),i.jsxs("div",{className:"bg-gray-900/50 p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-700/50",children:[i.jsx("h3",{className:"text-xs font-medium text-gray-400 uppercase tracking-wider mb-2",children:"Social Links"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"bg-gray-800 p-2 rounded-lg mr-2",children:i.jsx("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{fillRule:"evenodd",d:"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z",clipRule:"evenodd"})})}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-xs font-medium text-gray-400",children:"GitHub"}),(Ce=c.socialLinks)!=null&&Ce.github?i.jsx("a",{href:c.socialLinks.github.startsWith("http")?c.socialLinks.github:`https://github.com/${c.socialLinks.github}`,className:"text-green-400 hover:text-green-300 hover:underline text-xs",target:"_blank",rel:"noopener noreferrer",children:c.socialLinks.github.startsWith("http")?c.socialLinks.github.replace(/https?:\/\/(www\.)?github\.com\//,"@"):`@${c.socialLinks.github}`}):i.jsx("span",{className:"text-gray-500 italic text-xs",children:"Not specified"})]})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"bg-gray-800 p-2 rounded-lg mr-2",children:i.jsx("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-xs font-medium text-gray-400",children:"LinkedIn"}),(Te=c.socialLinks)!=null&&Te.linkedin?i.jsxs("a",{href:`https://linkedin.com/in/${c.socialLinks.linkedin}`,className:"text-green-400 hover:text-green-300 hover:underline text-xs",target:"_blank",rel:"noopener noreferrer",children:["@",c.socialLinks.linkedin]}):i.jsx("span",{className:"text-gray-500 italic text-xs",children:"Not specified"})]})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"bg-gray-800 p-2 rounded-lg mr-2",children:i.jsx("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-xs font-medium text-gray-400",children:"Twitter"}),(We=c.socialLinks)!=null&&We.twitter?i.jsxs("a",{href:`https://twitter.com/${c.socialLinks.twitter}`,className:"text-green-400 hover:text-green-300 hover:underline text-xs",target:"_blank",rel:"noopener noreferrer",children:["@",c.socialLinks.twitter]}):i.jsx("span",{className:"text-gray-500 italic text-xs",children:"Not specified"})]})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"bg-gray-800 p-2 rounded-lg mr-2",children:i.jsx("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{fillRule:"evenodd",d:"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z",clipRule:"evenodd"})})}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-xs font-medium text-gray-400",children:"Instagram"}),(Ke=c.socialLinks)!=null&&Ke.instagram?i.jsxs("a",{href:`https://instagram.com/${c.socialLinks.instagram}`,className:"text-green-400 hover:text-green-300 hover:underline text-xs",target:"_blank",rel:"noopener noreferrer",children:["@",c.socialLinks.instagram]}):i.jsx("span",{className:"text-gray-500 italic text-xs",children:"Not specified"})]})]})]})]})]})]})},Up=()=>{const[l,s]=C.useState({notifications:{email:!0,push:!1,sms:!1},theme:"light",language:"en",codeEditor:{fontSize:"14px",tabSize:2,autoSave:!0,wordWrap:!1}}),[a,c]=C.useState(""),d=S=>{const{name:k,checked:j}=S.target;s(P=>({...P,notifications:{...P.notifications,[k]:j}}))},h=S=>{s(k=>({...k,theme:S.target.value}))},m=S=>{s(k=>({...k,language:S.target.value}))},v=S=>{const{name:k,value:j,type:P,checked:D}=S.target;s(U=>({...U,codeEditor:{...U.codeEditor,[k]:P==="checkbox"?D:j}}))},p=S=>{S.preventDefault(),c("Settings saved successfully"),setTimeout(()=>c(""),3e3)};return i.jsxs("div",{className:"bg-gray-800/70 backdrop-blur-sm p-6 rounded-lg border border-gray-700/50 shadow-lg",children:[i.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Settings"}),a&&i.jsx("div",{className:"bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:a})}),i.jsxs("form",{onSubmit:p,children:[i.jsxs("div",{className:"mb-8",children:[i.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Notifications"}),i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"checkbox",id:"email",name:"email",checked:l.notifications.email,onChange:d,className:"h-4 w-4 text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600 rounded"}),i.jsx("label",{htmlFor:"email",className:"ml-2 block text-sm text-gray-300",children:"Email notifications"})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"checkbox",id:"push",name:"push",checked:l.notifications.push,onChange:d,className:"h-4 w-4 text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600 rounded"}),i.jsx("label",{htmlFor:"push",className:"ml-2 block text-sm text-gray-300",children:"Push notifications"})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"checkbox",id:"sms",name:"sms",checked:l.notifications.sms,onChange:d,className:"h-4 w-4 text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600 rounded"}),i.jsx("label",{htmlFor:"sms",className:"ml-2 block text-sm text-gray-300",children:"SMS notifications"})]})]})]}),i.jsxs("div",{className:"mb-8",children:[i.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Appearance"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"theme",className:"block text-sm font-medium text-gray-300 mb-1",children:"Theme"}),i.jsxs("select",{id:"theme",value:l.theme,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",children:[i.jsx("option",{value:"light",children:"Light"}),i.jsx("option",{value:"dark",children:"Dark"}),i.jsx("option",{value:"system",children:"System Default"})]})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"language",className:"block text-sm font-medium text-gray-300 mb-1",children:"Language"}),i.jsxs("select",{id:"language",value:l.language,onChange:m,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",children:[i.jsx("option",{value:"en",children:"English"}),i.jsx("option",{value:"es",children:"Spanish"}),i.jsx("option",{value:"fr",children:"French"}),i.jsx("option",{value:"de",children:"German"}),i.jsx("option",{value:"zh",children:"Chinese"})]})]})]})]}),i.jsxs("div",{className:"mb-8",children:[i.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Code Editor"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"fontSize",className:"block text-sm font-medium text-gray-300 mb-1",children:"Font Size"}),i.jsxs("select",{id:"fontSize",name:"fontSize",value:l.codeEditor.fontSize,onChange:v,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",children:[i.jsx("option",{value:"12px",children:"12px"}),i.jsx("option",{value:"14px",children:"14px"}),i.jsx("option",{value:"16px",children:"16px"}),i.jsx("option",{value:"18px",children:"18px"}),i.jsx("option",{value:"20px",children:"20px"})]})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"tabSize",className:"block text-sm font-medium text-gray-300 mb-1",children:"Tab Size"}),i.jsxs("select",{id:"tabSize",name:"tabSize",value:l.codeEditor.tabSize,onChange:v,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",children:[i.jsx("option",{value:2,children:"2 spaces"}),i.jsx("option",{value:4,children:"4 spaces"}),i.jsx("option",{value:8,children:"8 spaces"})]})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"checkbox",id:"autoSave",name:"autoSave",checked:l.codeEditor.autoSave,onChange:v,className:"h-4 w-4 text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600 rounded"}),i.jsx("label",{htmlFor:"autoSave",className:"ml-2 block text-sm text-gray-300",children:"Auto Save"})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("input",{type:"checkbox",id:"wordWrap",name:"wordWrap",checked:l.codeEditor.wordWrap,onChange:v,className:"h-4 w-4 text-green-500 focus:ring-green-400 bg-gray-700 border-gray-600 rounded"}),i.jsx("label",{htmlFor:"wordWrap",className:"ml-2 block text-sm text-gray-300",children:"Word Wrap"})]})]})]}),i.jsx("div",{className:"pt-4",children:i.jsxs("button",{type:"submit",className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Save Settings"})]})})]})]})},Ap=()=>{const[l,s]=C.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[a,c]=C.useState(""),[d,h]=C.useState(""),m=p=>{const{name:S,value:k}=p.target;s(j=>({...j,[S]:k}))},v=p=>{if(p.preventDefault(),h(""),c(""),!l.currentPassword||!l.newPassword||!l.confirmPassword){h("Please fill in all fields");return}if(l.newPassword!==l.confirmPassword){h("New passwords do not match");return}if(l.newPassword.length<8){h("New password must be at least 8 characters long");return}c("Password changed successfully"),s({currentPassword:"",newPassword:"",confirmPassword:""})};return i.jsxs("div",{className:"bg-gray-800/70 backdrop-blur-sm p-6 rounded-lg border border-gray-700/50 shadow-lg",children:[i.jsx("h2",{className:"text-2xl font-bold text-white mb-6",children:"Change Password"}),d&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:d})}),a&&i.jsx("div",{className:"bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded-lg mb-4",role:"alert",children:i.jsx("span",{className:"block sm:inline",children:a})}),i.jsxs("form",{onSubmit:v,className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-300 mb-1",children:"Current Password"}),i.jsx("input",{type:"password",id:"currentPassword",name:"currentPassword",value:l.currentPassword,onChange:m,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-300 mb-1",children:"New Password"}),i.jsx("input",{type:"password",id:"newPassword",name:"newPassword",value:l.newPassword,onChange:m,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300 mb-1",children:"Confirm New Password"}),i.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:l.confirmPassword,onChange:m,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",required:!0})]}),i.jsx("div",{className:"pt-4",children:i.jsxs("button",{type:"submit",className:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Change Password"})]})})]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:"Password Requirements"}),i.jsxs("ul",{className:"list-disc pl-5 text-sm text-gray-400 space-y-1",children:[i.jsx("li",{children:"At least 8 characters long"}),i.jsx("li",{children:"Include at least one uppercase letter"}),i.jsx("li",{children:"Include at least one number"}),i.jsx("li",{children:"Include at least one special character"})]})]})]})},Bp=()=>(Gr(),C.useEffect(()=>{window.history.pushState(null,"",window.location.pathname);const l=()=>{window.history.pushState(null,"",window.location.pathname)};return window.addEventListener("popstate",l),()=>{window.removeEventListener("popstate",l)}},[]),i.jsxs("div",{className:"h-full",children:[i.jsxs("div",{className:"bg-gray-800/70 backdrop-blur-sm p-6 rounded-lg border border-gray-700/50 shadow-lg mb-6",children:[i.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Code Editor"}),i.jsx("p",{className:"text-gray-300",children:"Welcome to your coding workspace!"})]}),i.jsxs("div",{className:"bg-gray-800/70 backdrop-blur-sm p-6 rounded-lg border border-gray-700/50 shadow-lg",children:[i.jsxs("div",{className:"flex justify-between mb-6",children:[i.jsx("div",{children:i.jsxs("select",{className:"px-3 py-2 bg-gray-700 border border-gray-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500",children:[i.jsx("option",{children:"JavaScript"}),i.jsx("option",{children:"Python"}),i.jsx("option",{children:"Java"}),i.jsx("option",{children:"C++"}),i.jsx("option",{children:"HTML"}),i.jsx("option",{children:"CSS"})]})}),i.jsx("div",{children:i.jsxs("button",{className:"ml-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 relative overflow-hidden group",children:[i.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-br from-green-500/0 via-green-500/30 to-green-500/0 opacity-0 group-hover:opacity-100 group-hover:animate-shine"}),i.jsx("span",{className:"relative",children:"Run Code"})]})})]}),i.jsxs("div",{className:"border border-gray-600 rounded-md overflow-hidden",children:[i.jsxs("div",{className:"bg-gray-700 px-4 py-2 border-b border-gray-600 flex justify-between items-center",children:[i.jsx("span",{className:"font-medium text-white",children:"main.js"}),i.jsxs("div",{children:[i.jsxs("button",{className:"text-green-400 hover:text-green-300 mr-3",children:[i.jsx("span",{className:"sr-only",children:"Save"}),i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{d:"M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h1a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h1v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z"})})]}),i.jsxs("button",{className:"text-green-400 hover:text-green-300",children:[i.jsx("span",{className:"sr-only",children:"Settings"}),i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:i.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})})]})]})]}),i.jsx("textarea",{className:"w-full h-64 p-4 font-mono text-sm bg-gray-800 text-gray-200 focus:outline-none focus:ring-1 focus:ring-green-500",placeholder:"// Write your code here",defaultValue:`// Welcome to CodeSpace Editor

function greet(name) {
  return 'Hello, ' + name + '!';
}

console.log(greet('World'));
`})]}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"font-medium text-gray-300 mb-2",children:"Output"}),i.jsx("div",{className:"bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm h-32 overflow-y-auto border border-gray-700",children:i.jsx("p",{children:"Hello, World!"})})]})]})]})),Vp=()=>{const[l]=nf(),s=Gr(),[a,c]=C.useState(""),[d,h]=C.useState(!0),{setAuthState:m}=Zn(),[v,p]=C.useState(""),[S,k]=C.useState(!1);if(localStorage.getItem("isAuthenticated")==="true"&&!l.get("token"))return i.jsx(ji,{to:"/dashboard",replace:!0});const j=P=>{s(P,{replace:!0}),window.history.pushState(null,"",P);const D=()=>{window.history.pushState(null,"",P)};window.addEventListener("popstate",D),window._preventBackNavigation=D};return C.useEffect(()=>((async()=>{try{const D=l.get("token"),U=l.get("userId"),F=l.get("error"),_=l.get("message"),Z=l.get("accountLinked")==="true";if(F){c(`${_||`Authentication failed: ${F}`}`),h(!1);return}if(!D||!U){c("Invalid authentication response"),h(!1);return}localStorage.setItem("token",D),localStorage.setItem("isAuthenticated","true"),console.log("Fetching user data using API service");const $=await af("/auth/me",!0);if(console.log("User data fetched successfully:",$),!$.success)throw new Error($.message||"Failed to fetch user data");localStorage.setItem("user",JSON.stringify($.user)),m({isAuthenticated:!0,user:$.user}),Z?(p(`Your Google account has been successfully linked to your existing account (${$.user.email}).`),k(!0),h(!1),setTimeout(()=>{j("/dashboard")},3e3)):j("/dashboard")}catch(D){console.error("Social callback error:",D),c(D.message||"Authentication failed"),h(!1)}})(),()=>{window._preventBackNavigation&&(window.removeEventListener("popstate",window._preventBackNavigation),delete window._preventBackNavigation)}),[l,s,m]),d?i.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-900 p-4",children:i.jsxs("div",{className:"w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-xl shadow-lg",children:[i.jsxs("div",{className:"text-center",children:[i.jsx("h2",{className:"text-3xl font-bold text-white",children:"Completing Login"}),i.jsx("p",{className:"mt-2 text-gray-400",children:"Please wait while we complete your authentication..."})]}),i.jsx("div",{className:"flex justify-center mt-6",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"})})]})}):S?i.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-900 p-4",children:i.jsxs("div",{className:"w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-xl shadow-lg",children:[i.jsxs("div",{className:"text-center",children:[i.jsx("h2",{className:"text-3xl font-bold text-white",children:"Success!"}),i.jsx("div",{className:"mt-4 flex justify-center",children:i.jsx("svg",{className:"h-16 w-16 text-green-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),i.jsx("p",{className:"mt-4 text-green-400",children:v}),i.jsx("p",{className:"mt-2 text-gray-400",children:"Redirecting to dashboard..."})]}),i.jsx("div",{className:"flex justify-center mt-6",children:i.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"})})]})}):a?i.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-900 p-4",children:i.jsxs("div",{className:"w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-xl shadow-lg",children:[i.jsxs("div",{className:"text-center",children:[i.jsx("h2",{className:"text-3xl font-bold text-white",children:"Authentication Failed"}),i.jsx("p",{className:"mt-2 text-red-400",children:a})]}),i.jsx("div",{className:"flex justify-center mt-6",children:i.jsx("button",{onClick:()=>s("/",{replace:!0}),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Back to Login"})})]})}):null},Wp=({children:l})=>localStorage.getItem("isAuthenticated")==="true"?l:i.jsx(ji,{to:"/",replace:!0}),Hp=({children:l})=>localStorage.getItem("isAuthenticated")==="true"?i.jsx(ji,{to:"/dashboard",replace:!0}):l,$p=Rm([{path:"/",element:i.jsx(Hp,{children:i.jsx(Qm,{})}),children:[{index:!0,element:i.jsx(Sp,{})},{path:"login",element:i.jsx(ff,{})},{path:"register",element:i.jsx(hf,{})},{path:"forgot-password",element:i.jsx(mf,{})},{path:"auth/social-callback",element:i.jsx(Vp,{})}]},{path:"/",element:i.jsx(Wp,{children:i.jsx(Km,{})}),children:[{path:"dashboard",element:i.jsx(Bp,{})},{path:"profile",element:i.jsx(Ip,{})},{path:"settings",element:i.jsx(Up,{})},{path:"change-password",element:i.jsx(Ap,{})}]},{path:"*",element:i.jsx(ji,{to:"/",replace:!0})}]);ih.createRoot(document.getElementById("root")).render(i.jsx(C.StrictMode,{children:i.jsx(Um,{router:$p})}));
