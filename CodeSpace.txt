# CodeSpace Application - Detailed Technical Report

## Overview
CodeSpace is a comprehensive full-stack web application designed to provide a collaborative coding environment with robust user authentication, detailed profile management, and real-time collaborative features. The application is structured as a modern client-server architecture with two distinct components:

1. **CSAPI** - A RESTful backend API built with Node.js, Express, and MongoDB that handles data persistence, authentication, and business logic
2. **CSUI** - A responsive frontend application built with React, Vite, and TailwindCSS that provides an intuitive user interface and seamless user experience

## Technology Stack - Detailed Breakdown

### Backend (CSAPI)
- **Runtime Environment**: Node.js - Server-side JavaScript runtime
- **Framework**: Express.js 5.1.0 - Fast, unopinionated, minimalist web framework
- **Database**:
  - MongoDB 8.13.2 - NoSQL document database
  - Mongoose ODM - Elegant MongoDB object modeling
- **Authentication**:
  - JSON Web Tokens (JWT) 9.0.2 - Secure token-based authentication
  - Passport.js 0.7.0 - Authentication middleware with strategies:
    - passport-google-oauth20 2.0.0 - Google OAuth 2.0 authentication
    - passport-github2 0.1.12 - GitHub OAuth authentication
- **Security**:
  - bcrypt 5.1.1 - Password hashing
  - CORS middleware - Cross-Origin Resource Sharing protection
- **Email Service**:
  - Nodemailer 6.10.1 - Email sending capabilities for OTP verification
- **Real-time Communication**:
  - Socket.io 4.8.1 - Bidirectional event-based communication

### Frontend (CSUI)
- **Framework**: React 19.0.0 - UI library with hooks and functional components
- **Build Tool**: Vite 6.3.1 - Next-generation frontend tooling
- **Routing**: React Router DOM 7.5.1 - Declarative routing for React
- **Styling**:
  - TailwindCSS 3.4.0 - Utility-first CSS framework
  - PostCSS 8.4.32 - CSS processing tool
  - Autoprefixer 10.4.16 - Vendor prefix management
- **State Management**:
  - Zustand 5.0.3 - Lightweight state management
  - Local storage - Persistent authentication state
- **UI Components**:
  - React Icons 5.5.0 - Popular icon libraries as React components
  - React Transition Group 4.4.5 - Animation utilities
- **Development Tools**:
  - ESLint 9.22.0 - JavaScript linting
  - TypeScript types - Type checking for React components

## Detailed File Structure and Purpose

### Backend (CSAPI)

#### Configuration Files
- **config/db.js**: Establishes and manages MongoDB connection using Mongoose, with error handling and connection status logging
- **config/passport.js**: Configures Passport.js authentication strategies:
  - Google OAuth strategy with profile extraction and user creation/linking logic
  - GitHub OAuth strategy with similar functionality plus GitHub profile URL extraction

#### Controllers
- **controllers/authController.js**: Comprehensive authentication logic:
  - User registration with email validation
  - OTP generation and verification
  - Login with JWT token generation
  - Password reset flow
  - OAuth callback handling for Google and GitHub
  - User profile retrieval

- **controllers/userController.js**: User profile management:
  - Profile updates (personal info, skills, education, experience)
  - Profile picture and cover photo upload/deletion
  - Password changes with security verification

#### Middleware
- **middleware/authMiddleware.js**: JWT verification middleware that:
  - Extracts and validates JWT tokens from request headers
  - Attaches authenticated user to request object
  - Handles expired or invalid tokens

- **middleware/corsMiddleware.js**: Configures CORS policy to:
  - Allow requests from the frontend origin
  - Set appropriate headers for cross-origin requests
  - Handle preflight requests

- **middleware/errorMiddleware.js**: Centralized error handling that:
  - Formats error responses consistently
  - Handles different error types (validation, authentication, server)
  - Provides appropriate status codes and messages

- **middleware/requestSizeLogger.js**: Monitors request payload sizes for performance optimization

#### Models
- **models/User.js**: Comprehensive user profile schema with:
  - Basic information (name, email)
  - Profile details (bio, title, location)
  - Social media links (GitHub, LinkedIn, Twitter, etc.)
  - Professional information (skills, education, experience)
  - Project portfolio
  - Automatic timestamp management

- **models/UserAuth.js**: Authentication-specific user data:
  - References User model via userId
  - Email and password (bcrypt hashed)
  - OAuth identifiers (googleId, githubId)
  - Authentication type tracking (local, Google, GitHub)
  - Verification status
  - Password reset tokens with expiry
  - Methods for password comparison

- **models/OTP.js**: One-Time Password management:
  - User association
  - Secure code storage
  - Purpose designation (verification vs. password reset)
  - Expiration tracking
  - Usage status
  - Automatic expiry via MongoDB TTL index
  - Validation methods

#### Routes
- **routes/authRoutes.js**: Defines authentication endpoints:
  - Registration and verification routes
  - Login and session management
  - Password reset flow
  - OAuth initiation and callback routes
  - Protected routes with JWT verification

- **routes/userRoutes.js**: User management endpoints:
  - Profile retrieval and updates
  - Media upload endpoints
  - Password change functionality
  - All routes protected by authentication middleware

#### Utilities
- **utils/emailUtils.js**: Email functionality:
  - Configures Nodemailer with environment variables
  - Templates for verification and password reset emails
  - Sending functions with error handling

- **utils/jwtUtils.js**: JWT operations:
  - Token generation with configurable expiry
  - Payload structure definition
  - Support for "remember me" functionality

- **utils/otpUtils.js**: OTP handling:
  - Secure random OTP generation
  - Expiry time calculation
  - Validation utilities

#### Core Files
- **app.js**: Application entry point:
  - Environment configuration
  - Middleware setup
  - Route registration
  - Error handling
  - Server initialization
  - Logging configuration

- **.env**: Environment-specific configuration:
  - Server settings (port, environment)
  - Database connection string
  - JWT secret and expiry times
  - Email service credentials
  - OAuth client IDs and secrets
  - Frontend URL for callbacks

### Frontend (CSUI)

#### Components
- **components/auth/**: Authentication-related components:
  - **OTPVerification.jsx**: Handles OTP input and verification with countdown timer
  - **SocialAuthCallback.jsx**: Processes OAuth provider callbacks and prevents back navigation
  - **PasswordStrengthMeter.jsx**: Visual password strength indicator

- **components/layout/**: Structural components:
  - **Header.jsx**: Application header with navigation and user menu
  - **Sidebar.jsx**: Navigation sidebar for authenticated users
  - **Footer.jsx**: Application footer with links and information

- **components/ui/**: Reusable UI elements:
  - **Button.jsx**: Styled button component with variants
  - **Input.jsx**: Form input with validation
  - **Modal.jsx**: Reusable modal dialog
  - **Toast.jsx**: Notification system

#### Layouts
- **layouts/InnerLayout.jsx**: Layout for authenticated pages with:
  - Header with user profile
  - Sidebar navigation
  - Main content area
  - Authentication verification

- **layouts/OuterLayout.jsx**: Layout for public pages with:
  - Simplified header
  - Full-width content area
  - Guest-only access control

#### Pages
- **pages/auth/**: Authentication pages:
  - **Login.jsx**: User login with email/password and OAuth options
  - **Register.jsx**: User registration with form validation
  - **ForgotPassword.jsx**: Password reset request
  - **Menu.jsx**: Initial auth selection screen

- **pages/codespace/**: Main application pages:
  - **CodeEditor.jsx**: Core coding environment with collaborative features

- **pages/user/**: User management pages:
  - **Profile.jsx**: Detailed user profile with editing capabilities
  - **Settings.jsx**: Application settings
  - **ChangePassword.jsx**: Secure password change functionality

#### Services
- **services/api.js**: Core API interaction:
  - Base API URL configuration
  - HTTP methods (GET, POST, PUT, DELETE)
  - Authentication header management
  - Error handling and response parsing

- **services/authService.js**: Authentication-specific API calls:
  - Login and registration
  - OTP verification
  - Password reset
  - OAuth initiation
  - Token management

- **services/userService.js**: User data operations:
  - Profile retrieval and updates
  - Media upload handling
  - Settings management

#### State Management
- **store/authStore.js**: Zustand store for authentication:
  - User authentication state
  - Login/logout actions
  - Token management
  - User profile data

- **store/uiStore.js**: UI state management:
  - Theme preferences
  - Sidebar state
  - Modal visibility
  - Toast notifications

#### Routing
- **Routes/Routes.jsx**: Application routing with:
  - Public routes with guest-only access
  - Protected routes with authentication requirements
  - Nested routes for layouts
  - Redirect handling

## Database Schema - Detailed

### User Collection
```javascript
{
  _id: ObjectId,
  name: String,                // User's full name
  email: String,               // Unique email address
  profilePicture: String,      // URL to profile image
  coverPhoto: String,          // URL to cover photo
  bio: String,                 // User biography
  title: String,               // Professional title
  location: String,            // Geographic location
  dateOfBirth: Date,           // Birth date
  website: String,             // Personal website
  socialLinks: {
    github: String,            // GitHub profile URL or username
    linkedin: String,          // LinkedIn profile
    twitter: String,           // Twitter handle
    instagram: String,         // Instagram username
    youtube: String            // YouTube channel
  },
  skills: [String],            // Array of skills
  education: [{                // Educational background
    institution: String,       // School/university name
    degree: String,            // Degree obtained
    fieldOfStudy: String,      // Major/concentration
    from: Date,                // Start date
    to: Date,                  // End date
    current: Boolean,          // Currently studying
    description: String        // Program description
  }],
  experience: [{               // Work experience
    company: String,           // Company name
    position: String,          // Job title
    from: Date,                // Start date
    to: Date,                  // End date
    current: Boolean,          // Current position
    description: String        // Job description
  }],
  projects: [{                 // Portfolio projects
    title: String,             // Project name
    description: String,       // Project details
    link: String,              // Project URL
    image: String,             // Screenshot/image
    technologies: [String]     // Technologies used
  }],
  createdAt: Date,             // Account creation timestamp
  updatedAt: Date              // Last update timestamp
}
```

### UserAuth Collection
```javascript
{
  _id: ObjectId,
  userId: ObjectId,            // Reference to User collection
  email: String,               // Email (matches User email)
  password: String,            // Bcrypt hashed password (for local auth)
  isVerified: Boolean,         // Email verification status
  googleId: String,            // Google OAuth ID (if applicable)
  githubId: String,            // GitHub OAuth ID (if applicable)
  authType: String,            // 'local', 'google', or 'github'
  resetPasswordToken: String,  // Password reset token
  resetPasswordExpire: Date,   // Token expiration
  lastLogin: Date,             // Last login timestamp
  createdAt: Date              // Record creation timestamp
}
```

### OTP Collection
```javascript
{
  _id: ObjectId,
  userId: ObjectId,            // Reference to User collection
  email: String,               // Email address
  code: String,                // OTP code
  purpose: String,             // 'verification' or 'password-reset'
  expiresAt: Date,             // Expiration timestamp
  isUsed: Boolean,             // Usage status
  createdAt: Date              // Creation timestamp
}
```

## Authentication Flows - Detailed

### Local Registration Process
1. **Email Availability Check**:
   - Frontend validates email format
   - Backend checks if email already exists
   - Returns availability status

2. **Registration Submission**:
   - Frontend collects and validates user data
   - Backend creates User document with basic info
   - Backend creates UserAuth document with hashed password
   - Sets isVerified to false

3. **OTP Generation and Delivery**:
   - Backend generates 6-digit OTP
   - Calculates expiry (10 minutes by default)
   - Creates OTP document in database
   - Sends formatted email with OTP via Nodemailer

4. **OTP Verification**:
   - User enters OTP in frontend
   - Backend validates OTP against database
   - Checks expiry and usage status
   - If valid, updates UserAuth.isVerified to true
   - Marks OTP as used

5. **Authentication**:
   - Backend generates JWT with user ID and expiry
   - Token is returned to frontend
   - Frontend stores token in localStorage
   - User is redirected to dashboard

### OAuth Authentication Flow (Google/GitHub)
1. **Initiation**:
   - User clicks OAuth provider button
   - Frontend redirects to `/api/auth/google` or `/api/auth/github`
   - Backend initiates Passport.js authentication flow
   - User is redirected to provider's consent screen

2. **Provider Authentication**:
   - User authenticates with provider
   - Provider redirects to callback URL with authorization code
   - Backend exchanges code for access token
   - Backend retrieves user profile from provider

3. **User Processing**:
   - Backend checks if user exists with provider ID
   - If exists, updates last login time
   - If not, checks if email exists in system:
     - If email exists, links provider ID to existing account
     - If email doesn't exist, creates new User and UserAuth records
   - For GitHub login, stores GitHub profile URL in user's socialLinks

4. **Authentication**:
   - Backend generates JWT with extended expiry (remember me)
   - Redirects to frontend callback URL with token
   - Frontend stores token and redirects to dashboard
   - Back navigation is prevented using history manipulation

### Password Reset Flow
1. **Reset Request**:
   - User submits email on forgot password form
   - Backend verifies email exists
   - Generates OTP for password reset purpose
   - Sends email with reset OTP

2. **OTP Verification**:
   - User enters OTP from email
   - Backend validates OTP for reset purpose
   - If valid, allows password reset

3. **Password Update**:
   - User submits new password
   - Backend validates password strength
   - Updates UserAuth with new hashed password
   - Invalidates all active reset OTPs
   - User is redirected to login

## API Endpoints - Detailed

### Authentication Endpoints
- `POST /api/auth/check-email`
  - **Purpose**: Check if email is available for registration
  - **Body**: `{ email: string }`
  - **Response**: `{ exists: boolean, isVerified: boolean }`

- `POST /api/auth/register`
  - **Purpose**: Register new user
  - **Body**: `{ name: string, email: string, password: string }`
  - **Response**: `{ success: boolean, message: string, userId: string }`

- `POST /api/auth/verify-otp`
  - **Purpose**: Verify OTP for account activation
  - **Body**: `{ userId: string, otp: string, purpose: string }`
  - **Response**: `{ success: boolean, token: string, user: object }`

- `POST /api/auth/resend-otp`
  - **Purpose**: Resend OTP if expired
  - **Body**: `{ userId: string, purpose: string }`
  - **Response**: `{ success: boolean, message: string }`

- `POST /api/auth/login`
  - **Purpose**: Authenticate user
  - **Body**: `{ email: string, password: string, rememberMe: boolean }`
  - **Response**: `{ success: boolean, token: string, user: object }`

- `POST /api/auth/forgot-password`
  - **Purpose**: Initiate password reset
  - **Body**: `{ email: string }`
  - **Response**: `{ success: boolean, message: string, userId: string }`

- `POST /api/auth/verify-reset-otp`
  - **Purpose**: Verify OTP for password reset
  - **Body**: `{ userId: string, otp: string }`
  - **Response**: `{ success: boolean, message: string }`

- `POST /api/auth/reset-password`
  - **Purpose**: Set new password
  - **Body**: `{ userId: string, password: string }`
  - **Response**: `{ success: boolean, message: string }`

- `GET /api/auth/me`
  - **Purpose**: Get current user profile
  - **Headers**: `Authorization: Bearer {token}`
  - **Response**: `{ success: boolean, user: object }`

- `POST /api/auth/change-password`
  - **Purpose**: Change password when logged in
  - **Headers**: `Authorization: Bearer {token}`
  - **Body**: `{ currentPassword: string, newPassword: string }`
  - **Response**: `{ success: boolean, message: string }`

### User Management Endpoints
- `PUT /api/users/profile`
  - **Purpose**: Update user profile
  - **Headers**: `Authorization: Bearer {token}`
  - **Body**: User profile fields
  - **Response**: `{ success: boolean, user: object }`

- `POST /api/users/upload-profile-picture`
  - **Purpose**: Upload profile picture
  - **Headers**: `Authorization: Bearer {token}`
  - **Body**: Form data with image
  - **Response**: `{ success: boolean, profilePicture: string }`

- `POST /api/users/upload-cover-photo`
  - **Purpose**: Upload cover photo
  - **Headers**: `Authorization: Bearer {token}`
  - **Body**: Form data with image
  - **Response**: `{ success: boolean, coverPhoto: string }`

- `DELETE /api/users/profile-picture`
  - **Purpose**: Remove profile picture
  - **Headers**: `Authorization: Bearer {token}`
  - **Response**: `{ success: boolean, message: string }`

- `DELETE /api/users/cover-photo`
  - **Purpose**: Remove cover photo
  - **Headers**: `Authorization: Bearer {token}`
  - **Response**: `{ success: boolean, message: string }`

## Frontend Architecture - Detailed

### Component Hierarchy
```
App
├── OuterLayout (Public)
│   ├── Home
│   │   ├── Menu
│   │   ├── Login
│   │   ├── Register
│   │   └── ForgotPassword
│   └── SocialAuthCallback
└── InnerLayout (Protected)
    ├── CodeEditor (Dashboard)
    ├── Profile
    ├── Settings
    └── ChangePassword
```

### State Management
- **authStore.js**: Central authentication state:
  - User information
  - Authentication status
  - Login/logout functions
  - Profile management
  - Token handling

- **Local Storage Persistence**:
  - JWT token
  - Authentication status
  - User ID
  - Remember me preference

### Route Protection
- **GuestRoute**: Prevents authenticated users from accessing public pages
- **ProtectedRoute**: Prevents unauthenticated users from accessing private pages
- **History Management**: Prevents back navigation after authentication

## Security Features
- **Password Security**:
  - Bcrypt hashing with salt
  - Minimum length requirements
  - Password strength meter

- **JWT Implementation**:
  - Short-lived tokens (1 day default)
  - Extended tokens for "remember me" (30 days)
  - Secure storage practices

- **OTP Security**:
  - Random 6-digit codes
  - 10-minute expiry
  - Single-use enforcement
  - Purpose-specific OTPs

- **OAuth Security**:
  - Email verification
  - Account linking protection
  - State parameter validation

- **Input Validation**:
  - Frontend form validation
  - Backend validation with error messages
  - MongoDB schema validation

## User Experience Features
- **Form Transitions**: Smooth animations between authentication forms
- **Responsive Design**: Mobile-friendly layouts with TailwindCSS
- **Error Handling**: User-friendly error messages
- **Loading States**: Visual feedback during async operations
- **Toast Notifications**: Non-intrusive success/error messages
