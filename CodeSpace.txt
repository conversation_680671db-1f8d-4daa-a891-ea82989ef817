# CodeSpace Application Report

## Overview
CodeSpace is a full-stack web application that provides a collaborative coding environment with user authentication, profile management, and real-time features. The application consists of two main parts:

1. **CSAPI** - Backend API built with Node.js, Express, and MongoDB
2. **CSUI** - Frontend application built with <PERSON>act, Vite, and TailwindCSS

## Technology Stack

### Backend (CSAPI)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT, Passport.js (Google OAuth, GitHub OAuth)
- **Email**: Nodemailer
- **Real-time**: Socket.io

### Frontend (CSUI)
- **Framework**: React 19
- **Build Tool**: Vite
- **Routing**: React Router v7
- **Styling**: TailwindCSS
- **State Management**: Zustand
- **Icons**: React Icons

## Folder Structure

### Backend (CSAPI)
```
CSAPI/
├── config/             # Configuration files
│   ├── db.js           # Database connection
│   └── passport.js     # Passport authentication strategies
├── controllers/        # Request handlers
│   ├── authController.js  # Authentication logic
│   └── userController.js  # User management logic
├── middleware/         # Express middleware
│   ├── authMiddleware.js  # JWT authentication
│   ├── corsMiddleware.js  # CORS configuration
│   └── errorMiddleware.js # Error handling
├── models/             # Mongoose models
│   ├── OTP.js          # OTP verification model
│   ├── User.js         # User profile model
│   └── UserAuth.js     # User authentication model
├── routes/             # API routes
│   ├── authRoutes.js   # Authentication routes
│   └── userRoutes.js   # User management routes
├── utils/              # Utility functions
│   ├── emailUtils.js   # Email sending functions
│   ├── jwtUtils.js     # JWT token generation
│   └── otpUtils.js     # OTP generation and validation
├── .env                # Environment variables
├── .env.example        # Example environment variables
├── app.js              # Main application entry point
└── package.json        # Dependencies and scripts
```

### Frontend (CSUI)
```
CSUI/
├── public/             # Static assets
│   └── favicon_io/     # Favicon files
├── src/                # Source code
│   ├── components/     # Reusable components
│   │   ├── auth/       # Authentication components
│   │   ├── layout/     # Layout components
│   │   └── ui/         # UI components
│   ├── layouts/        # Page layouts
│   │   ├── InnerLayout.jsx  # Authenticated layout
│   │   └── OuterLayout.jsx  # Public layout
│   ├── pages/          # Page components
│   │   ├── auth/       # Authentication pages
│   │   ├── codespace/  # Code editor pages
│   │   └── user/       # User profile pages
│   ├── Routes/         # Routing configuration
│   ├── services/       # API services
│   ├── store/          # Zustand state stores
│   ├── styles/         # CSS styles
│   ├── utils/          # Utility functions
│   ├── App.jsx         # Main App component
│   ├── index.css       # Global CSS
│   └── main.jsx        # Application entry point
├── index.html          # HTML template
├── package.json        # Dependencies and scripts
├── tailwind.config.js  # TailwindCSS configuration
└── vite.config.js      # Vite configuration
```

## Key Features

### Authentication System
- Email/Password registration with OTP verification
- Google OAuth authentication
- GitHub OAuth authentication
- Password reset functionality
- JWT-based authentication
- Remember me functionality
- Prevention of back navigation after login

### User Profile Management
- Profile information (name, bio, location, etc.)
- Profile picture and cover photo upload
- Social links (GitHub, LinkedIn, Twitter, etc.)
- Skills, education, and experience

### Code Editor (Dashboard)
- Real-time code editing (planned with Socket.io)
- Collaborative features (planned)

## Database Models

### User Model
- Basic profile information (name, email)
- Extended profile (bio, title, location, etc.)
- Social links
- Skills, education, experience, projects
- Profile picture and cover photo

### UserAuth Model
- References User model
- Authentication method (local, Google, GitHub)
- Password (hashed)
- OAuth IDs (Google, GitHub)
- Verification status
- Password reset tokens

### OTP Model
- One-time passwords for verification and password reset
- Expiry time
- Purpose (verification or password reset)
- Usage status

## Authentication Flow

### Local Registration
1. User submits registration form
2. Backend creates User and UserAuth records
3. OTP is generated and sent via email
4. User verifies email with OTP
5. User is authenticated and redirected to dashboard

### OAuth Authentication (Google/GitHub)
1. User clicks OAuth button
2. User is redirected to OAuth provider
3. Provider redirects back with user info
4. Backend creates or links User and UserAuth records
5. User is authenticated and redirected to dashboard

## API Endpoints

### Authentication
- `POST /api/auth/check-email` - Check if email exists
- `POST /api/auth/register` - Register new user
- `POST /api/auth/verify-otp` - Verify OTP
- `POST /api/auth/resend-otp` - Resend OTP
- `POST /api/auth/login` - Login user
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/verify-reset-otp` - Verify reset OTP
- `POST /api/auth/reset-password` - Reset password
- `GET /api/auth/google` - Google OAuth
- `GET /api/auth/google/callback` - Google OAuth callback
- `GET /api/auth/github` - GitHub OAuth
- `GET /api/auth/github/callback` - GitHub OAuth callback

### User Management
- `GET /api/auth/me` - Get current user
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/upload-profile-picture` - Upload profile picture
- `POST /api/users/upload-cover-photo` - Upload cover photo
- `DELETE /api/users/profile-picture` - Delete profile picture
- `DELETE /api/users/cover-photo` - Delete cover photo
- `POST /api/auth/change-password` - Change password

## Frontend Routes

### Public Routes
- `/` - Home page with login/register options
- `/login` - Login page
- `/register` - Registration page
- `/forgot-password` - Forgot password page
- `/auth/social-callback` - OAuth callback handler

### Protected Routes
- `/dashboard` - Code editor (main application)
- `/profile` - User profile
- `/settings` - User settings
- `/change-password` - Change password

## State Management
- Zustand stores for authentication and user data
- Local storage for persistent authentication

## Deployment
- Backend configured for IIS deployment (web.config)
- Frontend built with Vite for production deployment
