{"name": "csapi", "version": "1.0.0", "description": "Backend API for CodeSpace", "main": "app.js", "scripts": {"dev": "nodemon app.js", "start": "node app.js"}, "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.9"}}