# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/codespace

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN_DEFAULT=1d
JWT_EXPIRES_IN_REMEMBER=30d

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>

# OTP Configuration
OTP_EXPIRY_MINUTES=10

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Frontend URL (for OAuth callbacks)
FRONTEND_URL=http://localhost:5173
